'use client';

import { Breadcrumb } from '@/components/Breadcrumb';
import Link from 'next/link';
import { useEffect, useState } from 'react';

interface Post {
  title: string;
  description: string;
  slug: string;
  thumbnail: string;
  date: string;
}

export default function Posts() {
  const [posts, setPosts] = useState<Post[]>([]);

  useEffect(() => {
    // 在客户端加载文章数据
    const loadPosts = async () => {
      try {
        const response = await fetch('/api/posts');
        const data = await response.json();
        setPosts(data);
      } catch (error) {
        console.error('Failed to load posts:', error);
      }
    };

    loadPosts();
  }, []);

  return (
    <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative bg-gradient-to-b from-slate-50/80 via-white to-white">
      <Breadcrumb />
      
      <div className="prose max-w-none">
        <h1 className="text-3xl font-bold mb-8 text-gray-800">Latest Articles</h1>
        
        <div className="grid gap-8">
          {posts.map(post => (
            <article key={post.slug} className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
              <Link 
                href={`/posts/${post.slug}`}
                className="block no-underline group"
              >
                <div className="flex gap-6">
                  {post.thumbnail && (
                    <div className="hidden md:block flex-shrink-0">
                      <img 
                        src={post.thumbnail} 
                        alt={post.title}
                        className="w-48 h-32 object-cover rounded-lg"
                      />
                    </div>
                  )}
                  <div className="flex-grow">
                    <h2 className="text-xl font-semibold mb-2 text-gray-800 group-hover:text-indigo-600 transition-colors">
                      {post.title}
                    </h2>
                    <p className="text-gray-600 mb-4 line-clamp-2">
                      {post.description}
                    </p>
                    <div className="flex items-center text-sm text-gray-500">
                      <time dateTime={post.date}>
                        {new Date(post.date).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </time>
                      <span className="ml-auto text-indigo-600 group-hover:text-indigo-800">
                        Read more →
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            </article>
          ))}
        </div>

        {posts.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">Loading posts...</p>
          </div>
        )}
      </div>
    </main>
  );
} 