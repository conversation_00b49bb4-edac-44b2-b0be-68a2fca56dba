import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for JFIF to JPG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free JFIF to JPG Converter',
  description: 'Convert JFIF to JPG online for free. Fast batch conversion with no watermarks. Preserve quality and remove EXIF data. No installation needed!',
  keywords: 'jfif to jpg, jfif converter, jfif to jpeg, convert jfif, online converter, free converter',
  alternates: {
    canonical: `${baseUrl}/jfif-to-jpg`,
  },
  openGraph: {
    title: 'The Best Free JFIF to JPG Converter',
    description: 'Convert JFIF to JPG online for free. Fast batch conversion with no watermarks. Preserve quality and remove EXIF data. No installation needed!',
    url: `${baseUrl}/jfif-to-jpg`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/professional-jfif-to-jpg-converter.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free JFIF to JPG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free JFIF to JPG Converter',
    description: 'Convert JFIF to JPG online for free. Fast batch conversion with no watermarks. Preserve quality and remove EXIF data. No installation needed!',
    images: [`https://image.heic-tojpg.com/professional-jfif-to-jpg-converter.webp`],
  },
};

export default function JfifToJpgLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 