import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import crypto from 'crypto';

// Generate safe file name
function generateSafeFileName(originalName: string): string {
  // Generate 8-character random string
  const randomString = crypto.randomBytes(4).toString('hex');
  // Get timestamp
  const timestamp = Date.now();
  // Extract non-special characters from original filename
  const safeOriginalName = originalName
    .replace(/[^a-zA-Z0-9]/g, '') // Keep only letters and numbers
    .slice(0, 20); // Limit length

  return `${timestamp}-${randomString}${safeOriginalName ? '-' + safeOriginalName : ''}.svg`;
}

// Check if file is supported PNG format
function isSupportedFormat(fileName: string): boolean {
  const lowerName = fileName.toLowerCase();
  return lowerName.endsWith('.png');
}

// Initialize S3 client (Cloudflare R2)
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

// Simple PNG to SVG conversion by embedding the PNG as base64 in SVG
async function convertPngToSvg(buffer: Buffer, originalName: string): Promise<string> {
  try {
    // Get image metadata
    const metadata = await sharp(buffer).metadata();
    const width = metadata.width || 100;
    const height = metadata.height || 100;
    
    // Convert buffer to base64
    const base64Data = buffer.toString('base64');
    
    // Create SVG with embedded PNG
    const svgContent = `<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
     width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
  <title>Converted from ${originalName}</title>
  <image x="0" y="0" width="${width}" height="${height}" 
         xlink:href="data:image/png;base64,${base64Data}" />
</svg>`;
    
    return svgContent;
  } catch (error) {
    throw new Error('Failed to convert PNG to SVG');
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const removeExif = formData.get('removeExif') === 'true';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file type
    if (!isSupportedFormat(file.name)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only PNG files are supported.' },
        { status: 400 }
      );
    }

    // Read file content
    let buffer;
    try {
      buffer = Buffer.from(await file.arrayBuffer());
    } catch (error) {
      console.error('Error reading file:', error);
      return NextResponse.json(
        { error: 'Failed to read file' },
        { status: 500 }
      );
    }
    
    // Process PNG file and convert to SVG
    let svgContent;
    try {
      // If removeExif is true, process the image to remove metadata first
      if (removeExif) {
        buffer = await sharp(buffer)
          .png({ compressionLevel: 6 })
          .toBuffer();
      }
      
      svgContent = await convertPngToSvg(buffer, file.name);
    } catch (error) {
      console.error('Error converting image:', error);
      return NextResponse.json(
        { error: 'Failed to convert image to SVG' },
        { status: 500 }
      );
    }

    // Generate safe file name
    const fileName = generateSafeFileName(file.name);

    // Upload to Cloudflare R2
    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
          Key: fileName,
          Body: Buffer.from(svgContent, 'utf-8'),
          ContentType: 'image/svg+xml',
          // Add original filename as metadata
          Metadata: {
            originalName: encodeURIComponent(file.name),
            exifRemoved: removeExif.toString()
          }
        })
      );
    } catch (error) {
      console.error('Error uploading to R2:', error);
      return NextResponse.json(
        { error: 'Failed to upload converted file' },
        { status: 500 }
      );
    }

    // Return download API URL
    const downloadUrl = `/api/download?key=${encodeURIComponent(fileName)}`;

    return NextResponse.json({
      success: true,
      url: downloadUrl,
      originalName: file.name
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}