import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for WebP to GIF page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free WebP to GIF Converter Online',
  description: 'Convert WebP to GIF online for free. Fast batch conversion with animation preservation. No watermarks.',
  keywords: 'webp to gif, convert webp to gif, webp to gif converter, .webp to gif, convert .webp to gif',
  alternates: {
    canonical: `${baseUrl}/webp-to-gif`,
  },
  openGraph: {
    title: 'The Best Free WebP to GIF Converter Online',
    description: 'Convert WebP to GIF online for free. Fast batch conversion with animation preservation. No watermarks.',
    url: `${baseUrl}/webp-to-gif`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/webp-to-gif-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free WebP to GIF Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free WebP to GIF Converter Online',
    description: 'Convert WebP to GIF online for free. Fast batch conversion with animation preservation. No watermarks.',
    images: [`https://image.heic-tojpg.com/webp-to-gif-converter-tool.webp`],
  },
};

export default function WebpToGifLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 