import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for HEIC to GIF page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free HEIC to GIF Converter Online',
  description: 'Convert HEIC to GIF online for free. Fast batch conversion with no watermarks. Transform Apple photos to animated GIFs with high quality.',
  keywords: 'heic to gif, convert heic to gif, heic to gif converter, heic to gif converter free',
  alternates: {
    canonical: `${baseUrl}/heic-to-gif`,
  },
  openGraph: {
    title: 'The Best Free HEIC to GIF Converter Online',
    description: 'Convert HEIC to GIF online for free. Fast batch conversion with no watermarks. Transform Apple photos to animated GIFs with high quality.',
    url: `${baseUrl}/heic-to-gif`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/heic-to-gif-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free HEIC to GIF Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free HEIC to GIF Converter Online',
    description: 'Convert HEIC to GIF online for free. Fast batch conversion with no watermarks. Transform Apple photos to animated GIFs with high quality.',
    images: [`https://image.heic-tojpg.com/heic-to-gif-converter-tool.webp`],
  },
};

export default function HeicToGifLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 