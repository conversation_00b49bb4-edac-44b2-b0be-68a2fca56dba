// components/Footer.js
import Link from 'next/link';
import { FiGithub, FiMail } from 'react-icons/fi';

export function Footer() {
  return (
    <footer className="bg-gradient-to-b from-white to-gray-50 border-t border-gray-100">
      <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* About Section */}
          <div>
            <h3 className="text-sm font-semibold text-gray-800 uppercase tracking-wider">About</h3>
            <p className="mt-4 text-sm text-gray-600 leading-relaxed">
              <Link href="/">HEIC to JPG</Link> Converter is a free online tool that helps you convert iPhone HEIC photos to JPEG format easily. 
              Fast, secure, and no installation required.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-sm font-semibold text-gray-800 uppercase tracking-wider">Quick Links</h3>
            <ul className="mt-4 space-y-2">
              <li>
                <Link href="/webp-to-jpg" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  WebP to JPG
                </Link>
              </li>
              <li>
                <Link href="/webp-to-png" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  WebP to PNG
                </Link>
              </li>
              <li>
                <Link href="/jfif-to-jpg" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  JFIF to JPG
                </Link>
              </li>
              <li>
                <Link href="/jpg-to-pdf" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  JPG to PDF
                </Link>
              </li>
              <li>
                <Link href="/jpg-to-png" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  JPG to PNG
                </Link>
              </li>
              <li>
                <Link href="/jpg-to-svg" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  JPG to SVG
                </Link>
              </li>
              <li>
                <Link href="/jpg-to-webp" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  JPG to WebP
                </Link>
              </li>
              <li>
                <Link href="/svg-to-jpg" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  SVG to JPG
                </Link>
              </li>
              <li>
                <Link href="/svg-to-png" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  SVG to PNG
                </Link>
              </li>
              <li>
                <Link href="/avif-to-jpg" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  AVIF to JPG
                </Link>
              </li>
              <li>
                <Link href="/avif-to-png" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  AVIF to PNG
                </Link>
              </li>
              <li>
                <Link href="/avif-to-webp" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  AVIF to WebP
                </Link>
              </li>
              <li>
                <Link href="/jfif-to-png" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  JFIF to PNG
                </Link>
              </li>
              <li>
                <Link href="/" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  Home
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Related Tools */}
          <div>
            <h3 className="text-sm font-semibold text-gray-800 uppercase tracking-wider">Related Tools</h3>
            <ul className="mt-4 space-y-2">
              <li>
                <Link href="/png-to-jpg" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  PNG to JPG
                </Link>
              </li>
              <li>
                <Link href="/png-to-webp" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  PNG to WebP
                </Link>
              </li>
              <li>
                <Link href="/jpeg-to-jpg" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  JPEG to JPG
                </Link>
              </li>
              <li>
                <Link href="/jpg-to-jpeg" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  JPG to JPEG
                </Link>
              </li>
              <li>
                <Link href="/webp-to-avif" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  Webp to AVIF
                </Link>
              </li>
              <li>
                <Link href="/" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  HEIC to JPG
                </Link>
              </li>
              <li>
                <Link href="/webp-to-gif" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  WebP to GIF
                </Link>
              </li>
              <li>
                <Link href="/gif-to-avif" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  GIF to AVIF
                </Link>
              </li>
              <li>
                <Link href="/jpg-to-avif" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  JPG to AVIF
                </Link>
              </li>
              <li>
                <Link href="/png-to-svg" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  PNG to SVG
                </Link>
              </li>
              <li>
                <Link href="/heic-to-png" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  HEIC to PNG
                </Link>
              </li>
              <li>
                <Link href="/heic-to-webp" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  HEIC to WebP
                </Link>
              </li>
              <li>
                <Link href="/heic-to-gif" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  HEIC to GIF
                </Link>
              </li>
              <li>
                <Link href="/heic-to-avif" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  HEIC to AVIF
                </Link>
              </li>
              <li>
                <Link href="/jpg-to-heic" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  JPG to HEIC
                </Link>
              </li>
              <li>
                <Link href="/posts" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  Blog
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-sm text-gray-600 hover:text-indigo-600 transition-colors">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Features */}
          <div>
            <h3 className="text-sm font-semibold text-gray-800 uppercase tracking-wider">Features</h3>
            <ul className="mt-4 space-y-2">
              <li className="text-sm text-gray-600 flex items-center">
                <span className="text-indigo-500 mr-2">✓</span> Batch Conversion
              </li>
              <li className="text-sm text-gray-600 flex items-center">
                <span className="text-indigo-500 mr-2">✓</span> No Registration Required
              </li>
              <li className="text-sm text-gray-600 flex items-center">
                <span className="text-indigo-500 mr-2">✓</span> EXIF Data Control
              </li>
              <li className="text-sm text-gray-600 flex items-center">
                <span className="text-indigo-500 mr-2">✓</span> High Quality Output
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-center">
            {/* Copyright */}
            <p className="text-sm text-gray-500">
              © {new Date().getFullYear()} <Link href="/">heic-tojpg.com</Link>. All rights reserved.
            </p>

            {/* Contact Links */}
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <a
                href="mailto:<EMAIL>"
                className="text-gray-500 hover:text-indigo-600 transition-colors"
                aria-label="Email Support"
              >
                <FiMail className="h-5 w-5" />
              </a>
              
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}