import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for JFIF to PNG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free JFIF to PNG Converter Online',
  description: 'Convert JFIF to PNG online for free. Fast batch conversion with no watermarks. Preserve quality and add transparency. No installation needed!',
  keywords: 'jfif to png, .jfif to png, convert jfif to png, jfif to png converter, .jfif to .png',
  alternates: {
    canonical: `${baseUrl}/jfif-to-png`,
  },
  openGraph: {
    title: 'The Best Free JFIF to PNG Converter Online',
    description: 'Convert JFIF to PNG online for free. Fast batch conversion with no watermarks. Preserve quality and add transparency. No installation needed!',
    url: `${baseUrl}/jfif-to-png`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/jfif-to-png-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free JFIF to PNG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free JFIF to PNG Converter Online',
    description: 'Convert JFIF to PNG online for free. Fast batch conversion with no watermarks. Preserve quality and add transparency. No installation needed!',
    images: [`https://image.heic-tojpg.com/jfif-to-png-converter-tool.webp`],
  },
};

export default function JfifToPngLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 