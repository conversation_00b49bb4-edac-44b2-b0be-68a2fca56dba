import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for JPG to PNG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free JPG to PNG Converter',
  description: 'Convert JPG to PNG online for free. Fast batch conversion with no watermarks. Preserve quality and transform JPG photos to lossless PNG format.',
  keywords: 'jpg to png, convert jpg to png, jpg to png converter, change jpg to png, jpg to png converter online',
  alternates: {
    canonical: `${baseUrl}/jpg-to-png`,
  },
  openGraph: {
    title: 'The Best Free JPG to PNG Converter',
    description: 'Convert JPG to PNG online for free. Fast batch conversion with no watermarks. Preserve quality and transform JPG photos to lossless PNG format.',
    url: `${baseUrl}/jpg-to-png`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/professional-jpg-to-png-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free JPG to PNG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free JPG to PNG Converter',
    description: 'Convert JPG to PNG online for free. Fast batch conversion with no watermarks. Preserve quality and transform JPG photos to lossless PNG format.',
    images: [`https://image.heic-tojpg.com/professional-jpg-to-png-converter-tool.webp`],
  },
};

export default function JpgToPngLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 