'use client';

import { Breadcrumb } from '@/components/Breadcrumb';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Github, FiMail } from 'react-icons/fi';

export default function About() {
  return (
    <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative bg-gradient-to-b from-slate-50/80 via-white to-white">
      <Breadcrumb />
      
      <div className="prose max-w-none">
        <h1 className="text-3xl font-bold mb-8 text-gray-800">About HEIC to JPG Converter</h1>
        
        <section className="mb-12">
          <div className="bg-gradient-to-br from-indigo-50 to-white p-6 rounded-lg border border-indigo-100 mb-8">
            <h2 className="text-2xl font-semibold mb-4 text-gray-800">Our Mission</h2>
            <p className="text-gray-600">
              We created HEIC to JPG Converter with a simple mission: to provide a free, fast, and secure way for users to convert their HEIC photos to the universally compatible JPG format. Our goal is to eliminate the frustration of dealing with incompatible image formats and make photo sharing easier for everyone.
            </p>
          </div>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6 text-gray-800">Why Choose Our Converter?</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
              <div className="flex items-center mb-4">
                <FiCheck className="text-indigo-500 w-5 h-5 mr-2" />
                <h3 className="text-lg font-semibold text-gray-800">Free Forever</h3>
              </div>
              <p className="text-gray-600">
                Our service is and will always be completely free. No hidden fees, no subscriptions, just straightforward conversion.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
              <div className="flex items-center mb-4">
                <FiCheck className="text-indigo-500 w-5 h-5 mr-2" />
                <h3 className="text-lg font-semibold text-gray-800">Privacy First</h3>
              </div>
              <p className="text-gray-600">
                Your photos are important to you, and we respect that. Files are automatically deleted after conversion, and we never store your personal data.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
              <div className="flex items-center mb-4">
                <FiCheck className="text-indigo-500 w-5 h-5 mr-2" />
                <h3 className="text-lg font-semibold text-gray-800">User-Friendly</h3>
              </div>
              <p className="text-gray-600">
                Simple drag-and-drop interface, batch processing capabilities, and instant downloads make converting your photos a breeze.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
              <div className="flex items-center mb-4">
                <FiCheck className="text-indigo-500 w-5 h-5 mr-2" />
                <h3 className="text-lg font-semibold text-gray-800">High Quality</h3>
              </div>
              <p className="text-gray-600">
                Our advanced conversion algorithm ensures your photos maintain their quality while optimizing file size for easy sharing.
              </p>
            </div>
          </div>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6 text-gray-800">Our Commitment</h2>
          <div className="bg-gradient-to-r from-indigo-50 via-white to-indigo-50 p-6 rounded-lg border border-indigo-100">
            <p className="text-gray-600 mb-4">
              We&apos;re committed to providing the best possible service to our users. Our team continuously works on improving the converter&apos;s performance, security, and user experience. We believe that everyone should have access to simple, efficient tools for managing their digital photos.
            </p>
            <p className="text-gray-600">
              As technology evolves and new image formats emerge, we&apos;ll continue to adapt and enhance our service to meet your needs. Your feedback and suggestions help us make our converter better for everyone.
            </p>
          </div>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6 text-gray-800">Support Our Work</h2>
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
            <div className="flex items-center mb-4">
              <FiCoffee className="text-pink-500 w-5 h-5 mr-2" />
              <h3 className="text-lg font-semibold text-gray-800">Buy Us a Coffee</h3>
            </div>
            <p className="text-gray-600 mb-4">
              If you find our converter helpful, consider supporting our work. Your contribution helps us maintain and improve the service, keeping it free for everyone.
            </p>
            <a
              href="https://ko-fi.com/yourfriendlycreator"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700"
            >
              Support Us
            </a>
          </div>
        </section>

        <section>
          <h2 className="text-2xl font-semibold mb-6 text-gray-800">Contact Us</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <a
              href="mailto:<EMAIL>"
              className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200"
            >
              <div className="flex items-center">
                <FiMail className="text-indigo-500 w-5 h-5 mr-2" />
                <span className="text-gray-800"><EMAIL></span>
              </div>
            </a>
           
          </div>
        </section>
      </div>
    </main>
  );
}