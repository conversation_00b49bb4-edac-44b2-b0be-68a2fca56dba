import Link from 'next/link';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card"

export default function ArticleList({ articles, showMoreLink = true }) {
  return (
    <section>
      <div className="space-y-6">
        {articles.map(({ slug, title, description }) => (
          <Card key={slug}>
            <CardHeader>
              <Link 
                href={`/posts/${encodeURIComponent(slug)}`}
                className="text-blue-600 hover:text-blue-800 transition-colors inline-flex items-center gap-1"
              >
                <CardTitle>{title}</CardTitle>
                →
              </Link>
              <CardDescription>{description}</CardDescription>
            </CardHeader>
          </Card>
        ))}
      </div>
      {showMoreLink && (
        <div className="mt-8">
          <Link href="/posts" className="text-blue-600 hover:text-blue-800 transition-colors">
            View all articles →
          </Link>
        </div>
      )}
    </section>
  )
}