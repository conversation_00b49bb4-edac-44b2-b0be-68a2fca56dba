import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import os from 'os';
import { exec } from 'child_process';
import { promisify } from 'util';

const execPromise = promisify(exec);

// Generate safe file name
function generateSafeFileName(originalName: string): string {
  // Generate 8 character random string
  const randomString = crypto.randomBytes(4).toString('hex');
  // Get timestamp
  const timestamp = Date.now();
  // Extract non-special characters from original filename
  const safeOriginalName = originalName
    .replace(/[^a-zA-Z0-9]/g, '') // Only keep letters and numbers
    .slice(0, 20); // Limit length

  return `${timestamp}-${randomString}${safeOriginalName ? '-' + safeOriginalName : ''}.webp`;
}

// Check if file is supported AVIF format
function isSupportedFormat(fileName: string): boolean {
  const lowerName = fileName.toLowerCase();
  return lowerName.endsWith('.avif');
}

// Initialize S3 client (Cloudflare R2)
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

// Try to convert using Sharp directly
async function convertWithSharp(buffer: Buffer, quality: number, removeExif: boolean): Promise<Buffer | null> {
  try {
    let sharpInstance = sharp(buffer, {
      failOn: 'none',
      limitInputPixels: false,
      sequentialRead: true
    });
    
    // Set output to WebP format
    sharpInstance = sharpInstance.webp({
      quality: quality,
      lossless: false,
      smartSubsample: true,
      effort: 6
    });
    
    // Handle metadata
    if (removeExif) {
      sharpInstance = sharpInstance.withMetadata({
        exif: {
          IFD0: {
            Copyright: 'Processed by heic-tojpg.com',
            Software: 'heic-tojpg.com'
          }
        }
      });
    } else {
      sharpInstance = sharpInstance.withMetadata();
    }
    
    return await sharpInstance.toBuffer();
  } catch (error) {
    console.error('Sharp direct conversion failed:', error);
    return null;
  }
}

// Convert using FFmpeg if available
async function convertWithFFmpeg(inputPath: string, outputPath: string, quality: number): Promise<boolean> {
  try {
    // Check if FFmpeg is available
    await execPromise('ffmpeg -version');
    
    // Use FFmpeg to convert AVIF to WebP
    const cmd = `ffmpeg -i "${inputPath}" -c:v libwebp -quality ${quality} -y "${outputPath}"`;
    await execPromise(cmd);
    
    // Check if output file exists and has size
    const stats = fs.statSync(outputPath);
    return stats.size > 0;
  } catch (error) {
    console.error('FFmpeg conversion failed:', error);
    return false;
  }
}

// Try to extract image data via intermediate format
async function convertViaIntermediate(buffer: Buffer, quality: number): Promise<Buffer | null> {
  try {
    // First try to convert to PNG as intermediate format
    const pngBuffer = await sharp(buffer, {
      failOn: 'none',
      limitInputPixels: false
    })
    .png()
    .toBuffer();
    
    // Then convert PNG to WebP
    return await sharp(pngBuffer)
      .webp({ quality })
      .toBuffer();
  } catch (error) {
    console.error('Intermediate conversion failed:', error);
    return null;
  }
}

export async function POST(request: NextRequest) {
  // Create temporary files for processing
  const tempDir = os.tmpdir();
  const tempInputPath = path.join(tempDir, `input-${Date.now()}.avif`);
  const tempOutputPath = path.join(tempDir, `output-${Date.now()}.webp`);
  let filesToCleanup: string[] = [];
  
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const quality = parseInt(formData.get('quality') as string) || 85;
    const removeExif = formData.get('removeExif') === 'true';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file type
    if (!isSupportedFormat(file.name)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only AVIF files are supported.' },
        { status: 400 }
      );
    }

    // Read file content
    let buffer;
    try {
      buffer = Buffer.from(await file.arrayBuffer());
      
      // Write to temp file for FFmpeg processing
      fs.writeFileSync(tempInputPath, new Uint8Array(buffer));
      filesToCleanup.push(tempInputPath);
    } catch (error) {
      console.error('Error reading file:', error);
      return NextResponse.json(
        { error: 'Failed to read file' },
        { status: 500 }
      );
    }
    
    // Try multiple conversion methods
    let optimizedOutput: Buffer | null = null;
    
    // Method 1: Try Sharp direct conversion
    optimizedOutput = await convertWithSharp(buffer, quality, removeExif);
    
    // Method 2: Try FFmpeg if Sharp failed
    if (!optimizedOutput) {
      console.log('Trying FFmpeg conversion...');
      const ffmpegSuccess = await convertWithFFmpeg(tempInputPath, tempOutputPath, quality);
      if (ffmpegSuccess) {
        filesToCleanup.push(tempOutputPath);
        optimizedOutput = fs.readFileSync(tempOutputPath);
        console.log('FFmpeg conversion successful');
      }
    }
    
    // Method 3: Try conversion via intermediate format
    if (!optimizedOutput) {
      console.log('Trying conversion via intermediate format...');
      optimizedOutput = await convertViaIntermediate(buffer, quality);
    }
    
    // If all methods failed
    if (!optimizedOutput) {
      return NextResponse.json(
        { error: 'Failed to convert AVIF to WebP after multiple attempts' },
        { status: 500 }
      );
    }

    // Generate safe file name
    const fileName = generateSafeFileName(file.name);

    // Upload to Cloudflare R2
    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
          Key: fileName,
          Body: optimizedOutput,
          ContentType: 'image/webp',
          // Add original filename as metadata
          Metadata: {
            originalName: encodeURIComponent(file.name),
            exifRemoved: removeExif.toString()
          }
        })
      );
    } catch (error) {
      console.error('Error uploading to R2:', error);
      return NextResponse.json(
        { error: 'Failed to upload converted file' },
        { status: 500 }
      );
    }

    // Return download API URL
    const downloadUrl = `/api/download?key=${encodeURIComponent(fileName)}`;

    return NextResponse.json({
      success: true,
      url: downloadUrl,
      originalName: file.name
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  } finally {
    // Clean up temporary files
    for (const file of filesToCleanup) {
      try {
        if (fs.existsSync(file)) {
          fs.unlinkSync(file);
        }
      } catch (cleanupError) {
        console.error('Error cleaning up temp file:', cleanupError);
      }
    }
  }
} 