import React from 'react';

interface ConversionButtonsProps {
  onConvert: () => void;
  onClear: () => void;
  buyMeCoffeeUrl: string;
  isConvertDisabled: boolean;
}

const ConversionButtons: React.FC<ConversionButtonsProps> = ({ 
  onConvert, 
  onClear, 
  buyMeCoffeeUrl,
  isConvertDisabled
}) => {
  return (
    <div className="flex flex-wrap justify-center gap-4 my-6">
      <button
        onClick={onConvert}
        disabled={isConvertDisabled}
        className={`px-6 py-2 font-semibold rounded-md transition duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 
          ${isConvertDisabled 
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
            : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
      >
        Convert Case
      </button>
      <button
        onClick={onClear}
        className="px-6 py-2 bg-gray-200 text-gray-800 font-semibold rounded-md hover:bg-gray-300 transition duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50"
      >
        Clear All
      </button>
      <a
        href={buyMeCoffeeUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="px-6 py-2 bg-pink-500 text-white font-semibold rounded-md hover:bg-pink-600 transition duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-opacity-50"
      >
        ☕Buy Me a Coffee
      </a>
    </div>
  );
};

export default ConversionButtons;