import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for JPG to SVG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free JPG to SVG Converter Online',
  description: 'Convert JPG to SVG online for free. Batch processing with no watermarks, high-quality vectorization.',
  keywords: 'jpg to svg, jpg converter, convert jpg, jpg to svg converter, online converter, free converter',
  alternates: {
    canonical: `${baseUrl}/jpg-to-svg`,
  },
  openGraph: {
    title: 'The Best Free JPG to SVG Converter Online',
    description: 'Convert JPG to SVG online for free. Batch processing with no watermarks, high-quality vectorization. No software installation required for professional vector conversion!',
    url: `${baseUrl}/jpg-to-svg`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/jpg-to-svg-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free JPG to SVG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free JPG to SVG Converter Online',
    description: 'Convert JPG to SVG online for free. Batch processing with no watermarks, high-quality vectorization. No software installation required for professional vector conversion!',
    images: [`https://image.heic-tojpg.com/jpg-to-svg-converter-tool.webp`],
  },
};

export default function JpgToSvgLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 