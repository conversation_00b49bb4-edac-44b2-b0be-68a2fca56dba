import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import crypto from 'crypto';

// Generate safe file name
function generateSafeFileName(originalName: string): string {
  // Generate 8-character random string
  const randomString = crypto.randomBytes(4).toString('hex');
  // Get timestamp
  const timestamp = Date.now();
  // Extract non-special characters from original filename
  const safeOriginalName = originalName
    .replace(/[^a-zA-Z0-9]/g, '') // Keep only letters and numbers
    .slice(0, 20); // Limit length

  return `${timestamp}-${randomString}${safeOriginalName ? '-' + safeOriginalName : ''}.avif`;
}

// Check if file is supported WebP format
function isSupportedFormat(fileName: string): boolean {
  const lowerName = fileName.toLowerCase();
  return lowerName.endsWith('.webp');
}

// Initialize S3 client (Cloudflare R2)
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const quality = parseInt(formData.get('quality') as string) || 85;
    const removeExif = formData.get('removeExif') === 'true';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file type
    if (!isSupportedFormat(file.name)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only WebP files are supported.' },
        { status: 400 }
      );
    }

    // Read file content
    let buffer;
    try {
      buffer = Buffer.from(await file.arrayBuffer());
    } catch (error) {
      console.error('Error reading file:', error);
      return NextResponse.json(
        { error: 'Failed to read file' },
        { status: 500 }
      );
    }
    
    // Process WebP file conversion to AVIF
    let optimizedOutput;
    try {
      let sharpInstance = sharp(buffer);
      
      // Set output to AVIF format
      sharpInstance = sharpInstance.avif({
        quality: quality,
        effort: 6, // Maximum compression effort (0-9, higher is better compression)
        chromaSubsampling: '4:4:4' // Better quality, no chroma subsampling
      });
      
      // Handle EXIF information
      if (removeExif) {
        sharpInstance = sharpInstance.withMetadata({
          exif: {
            IFD0: {
              Copyright: 'Processed by heic-tojpg.com',
              Software: 'heic-tojpg.com'
            }
          }
        });
      } else {
        // Preserve color configuration
        sharpInstance = sharpInstance.withMetadata(); // Preserve all metadata including ICC profiles
      }

      optimizedOutput = await sharpInstance.toBuffer();
    } catch (error) {
      console.error('Error converting image:', error);
      return NextResponse.json(
        { error: 'Failed to convert image to AVIF' },
        { status: 500 }
      );
    }

    // Generate safe file name
    const fileName = generateSafeFileName(file.name);

    // Upload to Cloudflare R2
    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
          Key: fileName,
          Body: optimizedOutput,
          ContentType: 'image/avif',
          // Add original filename as metadata
          Metadata: {
            originalName: encodeURIComponent(file.name),
            exifRemoved: removeExif.toString()
          }
        })
      );
    } catch (error) {
      console.error('Error uploading to R2:', error);
      return NextResponse.json(
        { error: 'Failed to upload converted file' },
        { status: 500 }
      );
    }

    // Return download API URL
    const downloadUrl = `/api/download?key=${encodeURIComponent(fileName)}`;

    return NextResponse.json({
      success: true,
      url: downloadUrl,
      originalName: file.name
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}