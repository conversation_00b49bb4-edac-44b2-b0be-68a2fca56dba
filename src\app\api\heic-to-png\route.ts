import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import crypto from 'crypto';
import heicConvert from 'heic-convert';

// Generate safe file name
function generateSafeFileName(originalName: string): string {
  // Generate 8-character random string
  const randomString = crypto.randomBytes(4).toString('hex');
  // Get timestamp
  const timestamp = Date.now();
  // Extract non-special characters from original filename
  const safeOriginalName = originalName
    .replace(/[^a-zA-Z0-9]/g, '') // Keep only letters and numbers
    .slice(0, 20); // Limit length

  return `${timestamp}-${randomString}${safeOriginalName ? '-' + safeOriginalName : ''}.png`;
}

// Check if file is supported HEIC/HEIF format
function isSupportedFormat(fileName: string): boolean {
  const lowerName = fileName.toLowerCase();
  return lowerName.endsWith('.heic') || lowerName.endsWith('.heif');
}

// Initialize S3 client (Cloudflare R2)
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const quality = parseInt(formData.get('quality') as string) || 85;
    const removeExif = formData.get('removeExif') === 'true';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file type
    if (!isSupportedFormat(file.name)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only HEIC/HEIF files are supported.' },
        { status: 400 }
      );
    }

    // Read file content
    let buffer;
    try {
      buffer = Buffer.from(await file.arrayBuffer());
    } catch (error) {
      console.error('Error reading file:', error);
      return NextResponse.json(
        { error: 'Failed to read file' },
        { status: 500 }
      );
    }
    
    // Process HEIC file conversion to PNG
    let pngBuffer;
    try {
      // Use heic-convert library to convert HEIC to PNG
      pngBuffer = await heicConvert({
        buffer: buffer,
        format: 'PNG'
      });
    } catch (error) {
      console.error('Error converting HEIC to PNG:', error);
      return NextResponse.json(
        { error: 'Failed to convert HEIC to PNG' },
        { status: 500 }
      );
    }
    
    // Use sharp to further process PNG (if needed to remove EXIF)
    let optimizedOutput;
    try {
      let sharpInstance = sharp(pngBuffer);
      
      // If need to remove EXIF information
      if (removeExif) {
        sharpInstance = sharpInstance.withMetadata({});
      } else {
        // Keep color configuration
        sharpInstance = sharpInstance.withMetadata();  // Keep all metadata including ICC profiles
      }

      optimizedOutput = await sharpInstance.png({
        quality: quality,
        compressionLevel: 6,  // PNG compression level (0-9)
        adaptiveFiltering: true,
        palette: quality < 90  // Use palette for lower quality settings
      }).toBuffer();
    } catch (error) {
      console.error('Error optimizing PNG:', error);
      return NextResponse.json(
        { error: 'Failed to optimize PNG image' },
        { status: 500 }
      );
    }

    // Generate safe file name
    const fileName = generateSafeFileName(file.name);

    // Upload to Cloudflare R2
    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
          Key: fileName,
          Body: optimizedOutput,
          ContentType: 'image/png',
          // Add original filename as metadata
          Metadata: {
            originalName: encodeURIComponent(file.name),
            exifRemoved: removeExif.toString()
          }
        })
      );
    } catch (error) {
      console.error('Error uploading to R2:', error);
      return NextResponse.json(
        { error: 'Failed to upload converted file' },
        { status: 500 }
      );
    }

    // Return download API URL
    const downloadUrl = `/api/download?key=${encodeURIComponent(fileName)}`;

    return NextResponse.json({
      success: true,
      url: downloadUrl,
      originalName: file.name
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}