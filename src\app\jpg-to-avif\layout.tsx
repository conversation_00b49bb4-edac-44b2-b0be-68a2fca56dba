import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for JPG to AVIF page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free JPG to AVIF Converter',
  description: 'Convert JPG to AVIF online for free. Fast bulk conversion with superior compression. Reduce file size by up to 50% while maintaining quality.',
  keywords: 'jpg to avif, convert jpg to avif, jpg to avif free, jpg to avif converter, jpg to avif bulk',
  alternates: {
    canonical: `${baseUrl}/jpg-to-avif`,
  },
  openGraph: {
    title: 'The Best Free JPG to AVIF Converter',
    description: 'Convert JPG to AVIF online for free. Fast bulk conversion with superior compression. Reduce file size by up to 50% while maintaining quality.',
    url: `${baseUrl}/jpg-to-avif`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/jpg-to-avif-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free JPG to AVIF Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free JPG to AVIF Converter',
    description: 'Convert JPG to AVIF online for free. Fast bulk conversion with superior compression. Reduce file size by up to 50% while maintaining quality.',
    images: [`https://image.heic-tojpg.com/jpg-to-avif-converter-tool.webp`],
  },
};

export default function JpgToAvifLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 