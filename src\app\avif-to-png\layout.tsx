import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for AVIF to PNG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free AVIF to PNG Converter Online',
  description: 'Convert AVIF to PNG online for free. Fast batch conversion with high quality output and transparency support. No watermarks, no registration.',
  keywords: 'avif to png, convert avif to png, .avif to png, avif to png converter, .avif to .png',
  alternates: {
    canonical: `${baseUrl}/avif-to-png`,
  },
  openGraph: {
    title: 'The Best Free AVIF to PNG Converter Online',
    description: 'Convert AVIF to PNG online for free. Fast batch conversion with high quality output and transparency support. No watermarks, no registration.',
    url: `${baseUrl}/avif-to-png`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/avif-to-png-converter.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free AVIF to PNG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free AVIF to PNG Converter Online',
    description: 'Convert AVIF to PNG online for free. Fast batch conversion with high quality output and transparency support. No watermarks, no registration. ',
    images: [`https://image.heic-tojpg.com/avif-to-png-converter.webp`],
  },
};

export default function AvifToPngLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 