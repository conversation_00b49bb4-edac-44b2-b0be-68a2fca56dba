import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for HEIC to PNG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free HEIC to PNG Converter Online',
  description: 'Convert HEIC to PNG online for free. Fast batch conversion from iPhone photos with no watermarks. Preserve quality and transparency. No installation needed!',
  keywords: 'heic to png, convert heic to png, heic to png converter, .heic to png, .heic to .png',
  alternates: {
    canonical: `${baseUrl}/heic-to-png`,
  },
  openGraph: {
    title: 'The Best Free HEIC to PNG Converter Online',
    description: 'Convert HEIC to PNG online for free. Fast batch conversion from iPhone photos with no watermarks. Preserve quality and transparency. No installation needed!',
    url: `${baseUrl}/heic-to-png`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/what-is-heif-format.jpg`,
        width: 1200,
        height: 630,
        alt: 'The Best Free HEIC to PNG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free HEIC to PNG Converter Online',
    description: 'Convert HEIC to PNG online for free. Fast batch conversion from iPhone photos with no watermarks. Preserve quality and transparency. No installation needed!',
    images: [`https://image.heic-tojpg.com/what-is-heif-format.jpg`],
  },
};

export default function HeicToPngLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 