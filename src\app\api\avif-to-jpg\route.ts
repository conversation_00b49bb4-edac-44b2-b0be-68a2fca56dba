import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import os from 'os';
import { exec } from 'child_process';
import { promisify } from 'util';

const execPromise = promisify(exec);

// 生成安全的文件名
function generateSafeFileName(originalName: string): string {
  // 生成8位随机字符串
  const randomString = crypto.randomBytes(4).toString('hex');
  // 获取时间戳
  const timestamp = Date.now();
  // 提取原始文件名中的非特殊字符
  const safeOriginalName = originalName
    .replace(/[^a-zA-Z0-9]/g, '') // 只保留字母和数字
    .slice(0, 20); // 限制长度

  return `${timestamp}-${randomString}${safeOriginalName ? '-' + safeOriginalName : ''}.jpg`;
}

// 检查文件是否为支持的AVIF格式
function isSupportedFormat(fileName: string): boolean {
  const lowerName = fileName.toLowerCase();
  return lowerName.endsWith('.avif');
}

// 初始化 S3 客户端 (Cloudflare R2)
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

// 使用FFmpeg转换AVIF到JPG
async function convertWithFFmpeg(inputFilePath: string, quality: number): Promise<Buffer> {
  const tempDir = os.tmpdir();
  const outputPath = path.join(tempDir, `output-${Date.now()}.jpg`);
  
  try {
    // 使用FFmpeg转换，提高质量参数
    // FFmpeg的质量参数范围是0-31，0是最好，31是最差
    const qualityParam = Math.max(0, Math.min(5, 31 - Math.floor((quality / 100) * 31))); // 确保高质量输出
    
    // 使用更多高质量参数
    const ffmpegCmd = `ffmpeg -i "${inputFilePath}" -q:v ${qualityParam} -pix_fmt yuvj444p -compression_level 6 "${outputPath}"`;
    console.log(`Running FFmpeg command: ${ffmpegCmd}`);
    
    const { stdout, stderr } = await execPromise(ffmpegCmd);
    if (stderr) {
      console.log('FFmpeg stderr:', stderr);
    }
    
    // 读取输出文件
    const outputBuffer = fs.readFileSync(outputPath);
    
    // 清理临时文件
    fs.unlinkSync(outputPath);
    
    return outputBuffer;
  } catch (error) {
    // 确保清理临时文件
    try {
      if (fs.existsSync(outputPath)) fs.unlinkSync(outputPath);
    } catch (cleanupError) {
      console.error('Error cleaning up temp files:', cleanupError);
    }
    
    throw error;
  }
}

// 使用备用方法处理AVIF文件
async function processWithFallbackMethod(buffer: Buffer): Promise<Buffer> {
  // 创建一个基本的JPG图像作为替代
  const width = 800;
  const height = 600;
  
  // 使用Sharp创建一个简单的渐变图像
  return await sharp({
    create: {
      width: width,
      height: height,
      channels: 3,
      background: { r: 245, g: 245, b: 245 }
    }
  })
    .composite([
      {
        input: Buffer.from([
          255, 255, 255, 255, // 白色
          255, 0, 0, 255,     // 红色
          0, 255, 0, 255,     // 绿色
          0, 0, 255, 255      // 蓝色
        ]),
        raw: {
          width: 2,
          height: 2,
          channels: 4
        },
        tile: true,
        gravity: 'center'
      }
    ])
    .jpeg({
      quality: 100
    })
    .toBuffer();
}

// 检查ffmpeg是否可用
async function isFFmpegAvailable(): Promise<boolean> {
  try {
    await execPromise('which ffmpeg || command -v ffmpeg');
    return true;
  } catch (error) {
    console.warn('FFmpeg not found in system. Will use fallback conversion methods.');
    return false;
  }
}

export async function POST(request: NextRequest) {
  // 创建临时文件路径用于FFmpeg
  const tempDir = os.tmpdir();
  const tempInputPath = path.join(tempDir, `input-${Date.now()}.avif`);
  let needCleanup = false;

  // 检查ffmpeg是否可用
  const ffmpegAvailable = await isFFmpegAvailable();

  // 检查Sharp的AVIF支持
  console.log('Sharp format support:', sharp.format);
  
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const quality = parseInt(formData.get('quality') as string) || 85;
    const removeExif = formData.get('removeExif') === 'true';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // 检查文件类型
    if (!isSupportedFormat(file.name)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only AVIF files are supported.' },
        { status: 400 }
      );
    }

    // 读取文件内容
    let buffer;
    try {
      buffer = Buffer.from(await file.arrayBuffer());
      
      // 将buffer写入临时文件，用于FFmpeg
      fs.writeFileSync(tempInputPath, new Uint8Array(buffer));
      needCleanup = true;
    } catch (error) {
      console.error('Error reading file:', error);
      return NextResponse.json(
        { error: 'Failed to read file' },
        { status: 500 }
      );
    }
    
    // 处理AVIF文件转换为JPG
    let optimizedOutput;
    
    // 尝试使用FFmpeg转换（如果可用）
    if (ffmpegAvailable) {
      try {
        console.log(`Attempting conversion with FFmpeg for file: ${file.name}`);
        optimizedOutput = await convertWithFFmpeg(tempInputPath, quality);
        console.log(`FFmpeg conversion successful for file: ${file.name}`);
      } catch (ffmpegError) {
        console.error(`FFmpeg conversion failed for file ${file.name}:`, ffmpegError);
        // FFmpeg失败，继续尝试其他方法
        optimizedOutput = null;
      }
    } else {
      console.log(`FFmpeg not available, skipping FFmpeg conversion for file: ${file.name}`);
      optimizedOutput = null;
    }
    
    // 如果FFmpeg转换失败或不可用，尝试使用Sharp方法
    if (!optimizedOutput) {
      try {
        // 方法1: 直接AVIF到JPG转换
        console.log(`Attempting conversion with Sharp method 1 for file: ${file.name}`);

        let sharpInstance = sharp(buffer, {
          failOn: 'none',
          limitInputPixels: false
        });

        // 直接转换为JPG，不使用中间格式
        sharpInstance = sharpInstance.jpeg({
          quality: quality,
          chromaSubsampling: '4:4:4',
          mozjpeg: true
        });

        // 如果需要移除EXIF信息
        if (removeExif) {
          sharpInstance = sharpInstance.withMetadata({
            exif: {
              IFD0: {
                Copyright: 'Processed by heic-tojpg.com',
                Software: 'heic-tojpg.com'
              }
            }
          });
        } else {
          // 保留颜色配置
          sharpInstance = sharpInstance.withMetadata();  // 保留所有元数据包括ICC配置文件
        }

        optimizedOutput = await sharpInstance.toBuffer();
        console.log(`Sharp method 1 conversion successful for file: ${file.name}`);
      } catch (conversionError) {
        console.error(`Sharp method 1 failed for file ${file.name}:`, conversionError);

        try {
          // 方法2: 使用更简单的转换路径
          console.log(`Attempting conversion with Sharp method 2 for file: ${file.name}`);
          let sharpInstance = sharp(buffer, { failOn: 'none' });

          // 简化转换，不使用中间格式，提高质量
          optimizedOutput = await sharpInstance
            .jpeg({
              quality: Math.min(100, quality + 10),
              chromaSubsampling: '4:4:4'
            })
            .toBuffer();
          console.log(`Sharp method 2 conversion successful for file: ${file.name}`);
        } catch (fallbackError) {
          console.error(`Sharp method 2 failed for file ${file.name}:`, fallbackError);
          
          try {
            // 方法3: 使用libvips直接转换选项
            console.log('Attempting conversion with Sharp method 3...');
            let sharpInstance = sharp(buffer, { 
              failOn: 'none',
              limitInputPixels: false,  // 不限制输入像素数
              sequentialRead: true      // 顺序读取以提高兼容性
            });
            
            // 尝试直接转换为JPEG，提高质量
            optimizedOutput = await sharpInstance
              .toFormat('jpeg', { 
                quality: Math.min(100, quality + 10),
                chromaSubsampling: '4:4:4'
              })
              .toBuffer();
            console.log('Sharp method 3 conversion successful');
          } catch (lastResortError) {
            console.error('Sharp method 3 failed:', lastResortError);
            
            try {
              // 方法4: 使用备用方法生成一个包含错误信息的JPG图像
              console.log('Attempting conversion with fallback method...');
              optimizedOutput = await processWithFallbackMethod(buffer);
              console.log('Fallback method successful - created error image');
              
              // 生成安全的文件名
              const fileName = generateSafeFileName(file.name);
              
              // 上传错误图像到R2
              try {
                await s3Client.send(
                  new PutObjectCommand({
                    Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
                    Key: fileName,
                    Body: optimizedOutput,
                    ContentType: 'image/jpeg',
                    // 添加原始文件名作为元数据
                    Metadata: {
                      originalName: encodeURIComponent(file.name),
                      isErrorImage: 'true'
                    }
                  })
                );
                
                // 返回下载API的URL
                const downloadUrl = `/api/download?key=${encodeURIComponent(fileName)}`;
                
                // 返回特殊状态，表示我们生成了一个错误图像而不是实际转换
                return NextResponse.json({
                  success: false,
                  error: 'Failed to convert AVIF to JPG',
                  details: 'The AVIF format might be using features not supported by the converter. Please try a different AVIF file.',
                  errorImage: true,
                  url: downloadUrl // 使用实际的下载链接
                });
                
              } catch (uploadError) {
                console.error('Error uploading error image to R2:', uploadError);
                throw new Error('Failed to upload error image');
              }
              
            } catch (fallbackMethodError) {
              console.error('Fallback method failed:', fallbackMethodError);
              throw new Error('All conversion methods failed');
            }
          }
        }
      }
    }

    // 如果所有方法都失败，返回错误
    if (!optimizedOutput) {
      return NextResponse.json(
        { 
          error: 'Failed to convert AVIF to JPG',
          details: 'All conversion methods failed. The AVIF format might be using features not supported by the converter. Please try a different AVIF file.'
        },
        { status: 500 }
      );
    }

    // 生成安全的文件名
    const fileName = generateSafeFileName(file.name);

    // 上传到Cloudflare R2
    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
          Key: fileName,
          Body: optimizedOutput,
          ContentType: 'image/jpeg',
          // 添加原始文件名作为元数据
          Metadata: {
            originalName: encodeURIComponent(file.name),
            exifRemoved: removeExif.toString()
          }
        })
      );
    } catch (error) {
      console.error('Error uploading to R2:', error);
      return NextResponse.json(
        { error: 'Failed to upload converted file' },
        { status: 500 }
      );
    }

    // 返回下载API的URL
    const downloadUrl = `/api/download?key=${encodeURIComponent(fileName)}`;

    return NextResponse.json({
      success: true,
      url: downloadUrl,
      originalName: file.name
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  } finally {
    // 确保清理临时文件
    if (needCleanup && fs.existsSync(tempInputPath)) {
      try {
        fs.unlinkSync(tempInputPath);
      } catch (e) {
        console.error('Failed to clean up temporary file:', e);
      }
    }
  }
} 