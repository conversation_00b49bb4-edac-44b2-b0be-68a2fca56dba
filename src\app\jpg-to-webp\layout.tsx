import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for JPG to WebP page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free JPG to WebP Converter Online',
  description: 'Convert JPG to WebP online for free. Reduce file size by up to 34% while maintaining image quality. Fast batch conversion with no watermarks!',
  keywords: 'jpg to webp, convert jpg to webp, jpg to webp converter, jpg to webp free, converter jpg to webp',
  alternates: {
    canonical: `${baseUrl}/jpg-to-webp`,
  },
  openGraph: {
    title: 'The Best Free JPG to WebP Converter Online',
    description: 'Convert JPG to WebP online for free. Reduce file size by up to 34% while maintaining image quality. Fast batch conversion with no watermarks!',
    url: `${baseUrl}/jpg-to-webp`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/jpg-to-webp-converter-online.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free JPG to WebP Converter Online',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free JPG to WebP Converter Online',
    description: 'Convert JPG to WebP online for free. Reduce file size by up to 34% while maintaining image quality. Fast batch conversion with no watermarks!',
    images: [`https://image.heic-tojpg.com/jpg-to-webp-converter-online.webp`],
  },
};

export default function JpgToWebpLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 