import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import crypto from 'crypto';

// 生成安全的文件名
function generateSafeFileName(originalName: string): string {
  // 生成8位随机字符串
  const randomString = crypto.randomBytes(4).toString('hex');
  // 获取时间戳
  const timestamp = Date.now();
  // 提取原始文件名中的非特殊字符
  const safeOriginalName = originalName
    .replace(/[^a-zA-Z0-9]/g, '') // 只保留字母和数字
    .slice(0, 20); // 限制长度

  return `${timestamp}-${randomString}${safeOriginalName ? '-' + safeOriginalName : ''}.jpg`;
}

// 检查文件是否为支持的PNG格式
function isSupportedFormat(fileName: string): boolean {
  const lowerName = fileName.toLowerCase();
  return lowerName.endsWith('.png');
}

// 初始化 S3 客户端 (Cloudflare R2)
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const quality = parseInt(formData.get('quality') as string) || 85;
    const removeExif = formData.get('removeExif') === 'true';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // 检查文件类型
    if (!isSupportedFormat(file.name)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only PNG files are supported.' },
        { status: 400 }
      );
    }

    // 读取文件内容
    let buffer;
    try {
      buffer = Buffer.from(await file.arrayBuffer());
    } catch (error) {
      console.error('Error reading file:', error);
      return NextResponse.json(
        { error: 'Failed to read file' },
        { status: 500 }
      );
    }
    
    // 处理PNG文件转换为JPG
    let optimizedOutput;
    try {
      let sharpInstance = sharp(buffer);
      
      // 设置输出为JPG格式
      sharpInstance = sharpInstance.jpeg({
        quality: quality,
        chromaSubsampling: '4:4:4',  // 更好的质量，无色度子采样
        mozjpeg: true                // 使用mozjpeg获得更好的压缩
      });
      
      // 如果需要移除EXIF信息
      if (removeExif) {
        sharpInstance = sharpInstance.withMetadata({
          exif: {
            IFD0: {
              Copyright: 'Processed by heic-tojpg.com',
              Software: 'heic-tojpg.com'
            }
          }
        });
      } else {
        // 保留颜色配置
        sharpInstance = sharpInstance.withMetadata();  // 保留所有元数据包括ICC配置文件
      }

      optimizedOutput = await sharpInstance.toBuffer();
    } catch (error) {
      console.error('Error converting image:', error);
      return NextResponse.json(
        { error: 'Failed to convert image to JPG' },
        { status: 500 }
      );
    }

    // 生成安全的文件名
    const fileName = generateSafeFileName(file.name);

    // 上传到Cloudflare R2
    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
          Key: fileName,
          Body: optimizedOutput,
          ContentType: 'image/jpeg',
          // 添加原始文件名作为元数据
          Metadata: {
            originalName: encodeURIComponent(file.name),
            exifRemoved: removeExif.toString()
          }
        })
      );
    } catch (error) {
      console.error('Error uploading to R2:', error);
      return NextResponse.json(
        { error: 'Failed to upload converted file' },
        { status: 500 }
      );
    }

    // 返回下载API的URL
    const downloadUrl = `/api/download?key=${encodeURIComponent(fileName)}`;

    return NextResponse.json({
      success: true,
      url: downloadUrl,
      originalName: file.name
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 