type TitleCaseStyle = 'AP Style' | 'APA Style' | 'Chicago Style' | 'MLA Style' | 'NY Times Style' | 'Wikipedia Style' | 'UPPERCASE' | 'lowercase' | 'Sentence case' | 'Start Case' | 'camelCase' | 'PascalCase' | 'hyphen-case' | 'snake_case' | 'dot.case';

const lowercaseWords = ['a', 'an', 'and', 'as', 'at', 'but', 'by', 'for', 'in', 'of', 'on', 'or', 'the', 'to', 'via', 'with'];

function capitalizeFirstLetter(word: string): string {
  return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
}

function apStyle(text: string): string {
  return text.split(' ').map((word, index) => {
    if (index === 0 || index === text.split(' ').length - 1 || word.length >= 4 || !lowercaseWords.includes(word.toLowerCase())) {
      return capitalizeFirstLetter(word);
    }
    return word.toLowerCase();
  }).join(' ');
}

function apaStyle(text: string): string {
  return text.split(' ').map((word, index) => {
    if (index === 0 || (index > 0 && word.length >= 4) || !lowercaseWords.includes(word.toLowerCase())) {
      return capitalizeFirstLetter(word);
    }
    return word.toLowerCase();
  }).join(' ');
}

function chicagoStyle(text: string): string {
  return text.split(' ').map((word, index) => {
    if (index === 0 || !lowercaseWords.includes(word.toLowerCase())) {
      return capitalizeFirstLetter(word);
    }
    return word.toLowerCase();
  }).join(' ');
}

function mlaStyle(text: string): string {
  return text.split(' ').map((word, index) => {
    if (index === 0 || !['a', 'an', 'the', 'to', 'of', 'for', 'in', 'and', 'but', 'or'].includes(word.toLowerCase())) {
      return capitalizeFirstLetter(word);
    }
    return word.toLowerCase();
  }).join(' ');
}

function nyTimesStyle(text: string): string {
  return text.split(' ').map((word, index) => {
    if (index === 0 || word.length >= 5 || !lowercaseWords.includes(word.toLowerCase())) {
      return capitalizeFirstLetter(word);
    }
    return word.toLowerCase();
  }).join(' ');
}

function wikipediaStyle(text: string): string {
  const words = text.split(' ');
  return words.map((word, index) => {
    if (index === 0 || (index === words.length - 1 && word !== 'the')) {
      return capitalizeFirstLetter(word);
    }
    return word.toLowerCase();
  }).join(' ');
}

function upperCase(text: string): string {
  return text.toUpperCase();
}

function lowerCase(text: string): string {
  return text.toLowerCase();
}

function sentenceCase(text: string): string {
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
}

function startCase(text: string): string {
  return text.split(' ').map(capitalizeFirstLetter).join(' ');
}

function camelCase(text: string): string {
  return text.split(' ').map((word, index) => 
    index === 0 ? word.toLowerCase() : capitalizeFirstLetter(word)
  ).join('');
}

function pascalCase(text: string): string {
  return text.split(' ').map(capitalizeFirstLetter).join('');
}

function hyphenCase(text: string): string {
  return text.toLowerCase().replace(/\s+/g, '-');
}

function snakeCase(text: string): string {
  return text.toLowerCase().replace(/\s+/g, '_');
}

function dotCase(text: string): string {
  return text.toLowerCase().replace(/\s+/g, '.');
}

export function convertTitleCase(text: string, style: TitleCaseStyle): string {
  switch (style) {
    case 'AP Style': return apStyle(text);
    case 'APA Style': return apaStyle(text);
    case 'Chicago Style': return chicagoStyle(text);
    case 'MLA Style': return mlaStyle(text);
    case 'NY Times Style': return nyTimesStyle(text);
    case 'Wikipedia Style': return wikipediaStyle(text);
    case 'UPPERCASE': return upperCase(text);
    case 'lowercase': return lowerCase(text);
    case 'Sentence case': return sentenceCase(text);
    case 'Start Case': return startCase(text);
    case 'camelCase': return camelCase(text);
    case 'PascalCase': return pascalCase(text);
    case 'hyphen-case': return hyphenCase(text);
    case 'snake_case': return snakeCase(text);
    case 'dot.case': return dotCase(text);
    default: return text;
  }
}