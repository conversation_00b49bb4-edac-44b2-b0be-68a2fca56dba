'use client'

import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

// Main navigation items (8 most important)
const mainNavItems = [
  { path: '/', label: 'Home' },
  { path: '/webp-to-png', label: 'WebP to PNG' },
  { path: '/webp-to-jpg', label: 'WebP to JPG' },
  { path: '/jpg-to-pdf', label: 'JPG to PDF' },
  { path: '/jpg-to-png', label: 'JPG to PNG' },
  { path: '/jpg-to-jpeg', label: 'JPG to JPEG' },
  { path: '/jpg-to-svg', label: 'JPG to SVG' },
  { path: '/jpg-to-webp', label: 'JPG to WebP' },
]

// Additional items for dropdown
const dropdownNavItems = [
  { path: '/png-to-webp', label: 'PNG to WebP' },
  { path: '/png-to-jpg', label: 'PNG to JPG' },
  { path: '/jfif-to-jpg', label: 'JFIF to JPG' },
  { path: '/jfif-to-png', label: 'JFIF to PNG' },
  { path: '/jpeg-to-jpg', label: 'JPEG to JPG' },
  { path: '/avif-to-jpg', label: 'AVIF to JPG' },
  { path: '/avif-to-png', label: 'AVIF to PNG' },
  { path: '/avif-to-webp', label: 'AVIF to WebP' },
  { path: '/svg-to-jpg', label: 'SVG to JPG' },
  { path: '/svg-to-png', label: 'SVG to PNG' },
  { path: '/webp-to-avif', label: 'WebP to AVIF' },
  { path: '/webp-to-gif', label: 'WebP to GIF' },
  { path: '/jpg-to-avif', label: 'JPG to AVIF' },
  { path: '/heic-to-png', label: 'HEIC to PNG' },
  { path: '/heic-to-webp', label: 'HEIC to WebP' },
  { path: '/heic-to-gif', label: 'HEIC to GIF' },
  { path: '/heic-to-avif', label: 'HEIC to AVIF' },
  { path: '/gif-to-avif', label: 'GIF to AVIF' },
  { path: '/png-to-svg', label: 'PNG to SVG' },
  { path: '/jpg-to-heic', label: 'JPG to HEIC' },


]

export function Navigation() {
  const pathname = usePathname()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const dropdownRef = useRef(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false)
      }
    }
    
    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Check if current page is in dropdown
  const isCurrentPathInDropdown = dropdownNavItems.some(item => item.path === pathname)

  return (
    <header className="w-full border-b bg-background">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo and Brand Name */}
          <Link href="/" className="flex items-center space-x-3">
            <img 
              src="/logo.png" 
              alt="HEIC to JPG Converter logo" 
              className="h-11 w-auto"
            />
            <div className="flex flex-col">
              <span className="text-lg font-bold leading-tight">HEIC to JPG</span>
              {/* <span className="text-xs text-gray-500">Online Converter</span>*/}
            </div>
          </Link>

          {/* Navigation Links - Desktop */}
          <nav className="hidden md:flex items-center space-x-6">
            {mainNavItems.map((item) => (
              <Link
                key={item.path}
                href={item.path}
                className={`text-sm font-medium transition-colors hover:text-indigo-600
                  ${item.path === pathname ? "text-indigo-600" : "text-gray-600"}`}
              >
                {item.label}
              </Link>
            ))}
            
            {/* More Dropdown */}
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className={`text-sm font-medium transition-colors hover:text-indigo-600 flex items-center space-x-1
                  ${isCurrentPathInDropdown ? "text-indigo-600" : "text-gray-600"}`}
                aria-expanded={isDropdownOpen}
                aria-haspopup="true"
              >
                <span>More</span>
                <svg 
                  className={`h-4 w-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              {/* Dropdown Menu - Desktop (4 items per row) */}
              {isDropdownOpen && (
                <div className="absolute right-0 mt-2 w-[600px] bg-white rounded-md shadow-lg py-4 px-4 z-10 border border-gray-200">
                  <div className="grid grid-cols-4 gap-2">
                    {dropdownNavItems.map((item) => (
                      <Link
                        key={item.path}
                        href={item.path}
                        className={`px-3 py-2 text-sm transition-colors hover:bg-gray-100 rounded-md
                          ${item.path === pathname ? "text-indigo-600 bg-indigo-50" : "text-gray-700"}`}
                        onClick={() => setIsDropdownOpen(false)}
                      >
                        {item.label}
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </nav>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 rounded-md hover:bg-gray-100"
            aria-label="Toggle menu"
          >
            <svg
              className="h-6 w-6"
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              {isMenuOpen ? (
                <path d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4">
            <nav className="flex flex-col space-y-4">
              {/* Main navigation items - Mobile (3 items per row) */}
              <div className="grid grid-cols-3 gap-2">
                {mainNavItems.map((item) => (
                  <Link
                    key={item.path}
                    href={item.path}
                    className={`text-sm font-medium transition-colors hover:text-indigo-600 px-2 py-2 rounded-md text-center
                      ${item.path === pathname ? "text-indigo-600 bg-indigo-50" : "text-gray-600"}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.label}
                  </Link>
                ))}
              </div>
              
              {/* Divider */}
              <div className="border-t border-gray-200 my-2"></div>
              
              {/* More section header */}
              <div className="px-4 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                More Converters
              </div>
              
              {/* Dropdown items - Mobile (3 items per row) */}
              <div className="grid grid-cols-3 gap-2">
                {dropdownNavItems.map((item) => (
                  <Link
                    key={item.path}
                    href={item.path}
                    className={`text-sm font-medium transition-colors hover:text-indigo-600 px-2 py-2 rounded-md text-center
                      ${item.path === pathname ? "text-indigo-600 bg-indigo-50" : "text-gray-600"}`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.label}
                  </Link>
                ))}
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}