import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for HEIC to AVIF page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free HEIC to AVIF Converter Online',
  description: 'Convert HEIC to AVIF online for free. Fast batch conversion with no watermarks. Preserve HDR quality and color profiles. Free HEIC to AVIF converter.',
  keywords: 'heic to avif, convert heic to avif, heic to avif converter, heic to avif converter free',
  alternates: {
    canonical: `${baseUrl}/heic-to-avif`,
  },
  openGraph: {
    title: 'The Best Free HEIC to AVIF Converter Online',
    description: 'Convert HEIC to AVIF online for free. Fast batch conversion with no watermarks. Preserve HDR quality and color profiles. Free HEIC to AVIF converter.',
    url: `${baseUrl}/heic-to-avif`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/heic-to-avif-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free HEIC to AVIF Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free HEIC to AVIF Converter Online',
    description: 'Convert HEIC to AVIF online for free. Fast batch conversion with no watermarks. Preserve HDR quality and color profiles. Free HEIC to AVIF converter.',
    images: [`https://image.heic-tojpg.com/heic-to-avif-converter-tool.webp`],
  },
};

export default function HeicToAvifLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 