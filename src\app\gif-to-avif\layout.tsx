import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for GIF to AVIF page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free GIF to AVIF Converter Online',
  description: 'Convert GIF to AVIF online for free. Fast batch conversion with no watermarks. Preserve animation quality while reducing file size by up to 90%.',
  keywords: 'gif to avif, convert gif to avif, gif to avif converter, gif to avif converter free',
  alternates: {
    canonical: `${baseUrl}/gif-to-avif`,
  },
  openGraph: {
    title: 'The Best Free GIF to AVIF Converter Online',
    description: 'Convert GIF to AVIF online for free. Fast batch conversion with no watermarks. Preserve animation quality while reducing file size by up to 90%.',
    url: `${baseUrl}/gif-to-avif`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/gif-to-avif-converter-tool.jpg`,
        width: 1200,
        height: 630,
        alt: 'The Best Free GIF to AVIF Converter Online',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free GIF to AVIF Converter Online',
    description: 'Convert GIF to AVIF online for free. Fast batch conversion with no watermarks. Preserve animation quality while reducing file size by up to 90%.',
    images: [`https://image.heic-tojpg.com/gif-to-avif-converter-tool.jpg`],
  },
};

export default function GifToAvifLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 