import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import crypto from 'crypto';

// Generate safe file name
function generateSafeFileName(originalName: string): string {
  // Generate 8 character random string
  const randomString = crypto.randomBytes(4).toString('hex');
  // Get timestamp
  const timestamp = Date.now();
  // Extract non-special characters from original filename (optional)
  const safeOriginalName = originalName
    .replace(/[^a-zA-Z0-9]/g, '') // Keep only letters and numbers
    .slice(0, 20); // Limit length

  // Use .webp extension for output
  return `${timestamp}-${randomString}${safeOriginalName ? '-' + safeOriginalName : ''}.webp`;
}

// Check if file is PNG format
function isPngFormat(fileName: string): boolean {
  const lowerName = fileName.toLowerCase();
  return lowerName.endsWith('.png');
}

// Initialize S3 client (Cloudflare R2)
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const quality = parseInt(formData.get('quality') as string) || 85;
    const removeExif = formData.get('removeExif') === 'true';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file type
    if (!file.type && !isPngFormat(file.name)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only PNG files are supported.' },
        { status: 400 }
      );
    }

    // Read file content
    let buffer;
    try {
      buffer = Buffer.from(await file.arrayBuffer());
    } catch (error) {
      console.error('Error reading file:', error);
      return NextResponse.json(
        { error: 'Failed to read file' },
        { status: 500 }
      );
    }
    
    // Process PNG to WebP conversion
    let optimizedOutput;
    try {
      let sharpInstance = sharp(buffer);
      
      // Set WebP output format and quality
      sharpInstance = sharpInstance.webp({
        quality: quality,
        lossless: quality >= 95, // Use lossless mode for very high quality settings
        nearLossless: quality >= 90 && quality < 95, // Use near lossless for high quality
        alphaQuality: quality, // Match alpha quality with main quality
        smartSubsample: true // Use smart subsampling for better quality
      });
      
      // If EXIF removal is requested
      if (removeExif) {
        sharpInstance = sharpInstance.withMetadata({
          exif: {
            IFD0: {
              Copyright: 'Processed by heic-tojpg.com',
              Software: 'heic-tojpg.com'
            }
          }
        });
      } else {
        // Preserve color profiles
        sharpInstance = sharpInstance.withMetadata();  // This preserves all metadata including ICC profiles
      }

      optimizedOutput = await sharpInstance.toBuffer();
    } catch (error) {
      console.error('Error optimizing image:', error);
      return NextResponse.json(
        { error: 'Failed to optimize image' },
        { status: 500 }
      );
    }

    // Generate safe filename
    const fileName = generateSafeFileName(file.name);

    // Upload to Cloudflare R2
    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
          Key: fileName,
          Body: optimizedOutput,
          ContentType: 'image/webp',
          // Add original filename as metadata
          Metadata: {
            originalName: encodeURIComponent(file.name),
            exifRemoved: removeExif.toString()
          }
        })
      );
    } catch (error) {
      console.error('Error uploading to R2:', error);
      return NextResponse.json(
        { error: 'Failed to upload converted file' },
        { status: 500 }
      );
    }

    // Return URL for download API
    const downloadUrl = `/api/download?key=${encodeURIComponent(fileName)}`;

    return NextResponse.json({
      success: true,
      url: downloadUrl,
      originalName: file.name
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 