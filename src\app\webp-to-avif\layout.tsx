import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for WebP to PNG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free WebP to PNG Converter',
  description: 'Convert WebP to PNG online for free. Fast batch conversion with no watermarks. Preserve quality and transparency. No installation needed!',
  keywords: 'webp to png, webp converter, convert webp, online converter, free converter, webp to png converter',
  alternates: {
    canonical: `${baseUrl}/webp-to-png`,
  },
  openGraph: {
    title: 'The Best Free WebP to PNG Converter',
    description: 'Convert WebP to PNG online for free. Fast batch conversion with no watermarks. Preserve quality and transparency. No installation needed!',
    url: `${baseUrl}/webp-to-png`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/webp-to-png-converter.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free WebP to PNG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free WebP to PNG Converter',
    description: 'Convert WebP to PNG online for free. Fast batch conversion with no watermarks. Preserve quality and transparency. No installation needed!',
    images: [`https://image.heic-tojpg.com/professional-webp-to-png-converter-tool.webp`],
  },
};

export default function WebpToPngLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 