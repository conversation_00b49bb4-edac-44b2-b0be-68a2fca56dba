import { Metadata } from 'next';
import { FiArrowLeft } from 'react-icons/fi';
import Link from 'next/link';
import { Breadcrumb } from '@/components/Breadcrumb';
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { marked } from 'marked';
import { baseUrl } from '../../layout';

// 生成动态元数据
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  // 读取文章内容
  const postsDirectory = path.join(process.cwd(), 'data/md');
  const fullPath = path.join(postsDirectory, `${params.slug}.md`);
  
  try {
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { data } = matter(fileContents);
    
    return {
      title: data.title,
      description: data.description,
      alternates: {
        canonical: `${baseUrl}/posts/${params.slug}`,
      },
      openGraph: {
        title: data.title,
        description: data.description,
        type: 'article',
        url: `${baseUrl}/posts/${params.slug}`,
        images: [
          {
            url: data.thumbnail,
            width: 1200,
            height: 630,
            alt: data.title,
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title: data.title,
        description: data.description,
        images: [data.thumbnail],
      },
    };
  } catch (error) {
    return {
      title: 'Post Not Found',
      description: 'The requested blog post could not be found.',
      alternates: {
        canonical: `${baseUrl}/posts/${params.slug}`,
      },
    };
  }
}

// 定义Post接口类型
interface Post {
  title: string;
  description: string;
  date: string;
  thumbnail: string;
  content: string;
  slug: string;
}

// 获取文章内容
async function getPost(slug: string): Promise<Post | null> {
  const postsDirectory = path.join(process.cwd(), 'data/md');
  const fullPath = path.join(postsDirectory, `${slug}.md`);
  
  try {
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { data, content } = matter(fileContents);
    // 确保marked返回字符串而不是Promise
    const htmlContent = await Promise.resolve(marked(content));
    
    return {
      title: data.title,
      description: data.description,
      date: data.date,
      thumbnail: data.thumbnail,
      content: htmlContent,
      slug: slug,
    };
  } catch (error) {
    return null;
  }
}

export default async function PostPage({ params }: { params: { slug: string } }) {
  const post = await getPost(params.slug);

  if (!post) {
    return (
      <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative bg-gradient-to-b from-slate-50/80 via-white to-white">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Post Not Found</h1>
          <p className="text-gray-600 mb-8">The post you're looking for doesn't exist.</p>
          <Link 
            href="/posts"
            className="inline-flex items-center text-indigo-600 hover:text-indigo-800"
          >
            <FiArrowLeft className="mr-2" />
            Back to Blog
          </Link>
        </div>
      </main>
    );
  }

  return (
    <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative bg-gradient-to-b from-slate-50/80 via-white to-white">
      <Breadcrumb />
      
      <article className="prose max-w-none">
        {post.thumbnail && (
          <img
            src={post.thumbnail}
            alt={post.title}
            className="w-full h-64 md:h-96 object-cover rounded-lg mb-8"
          />
        )}
        
        <h1 className="text-3xl md:text-4xl font-bold mb-4 text-gray-800">{post.title}</h1>
        
        <div className="flex items-center text-sm text-gray-500 mb-8">
          <time dateTime={post.date}>
            {new Date(post.date).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </time>
        </div>

        <div 
          className="mt-8 prose prose-indigo prose-lg" 
          dangerouslySetInnerHTML={{ __html: post.content }} 
        />

        <div className="mt-12 pt-8 border-t">
          <Link 
            href="/posts"
            className="inline-flex items-center text-indigo-600 hover:text-indigo-800"
          >
            <FiArrowLeft className="mr-2" />
            Back to Blog
          </Link>
        </div>
      </article>
    </main>
  );
} 