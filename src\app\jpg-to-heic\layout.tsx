import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for JPG to HEIC page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free JPG to HEIC Converter Online',
  description: 'Convert JPG to HEIC online for free. Fast batch conversion with no watermarks. Save up to 50% storage space with superior HEIC compression.',
  keywords: 'jpg to heic, convert jpg to heic, change jpg to heic, jpg convert to heic, jpg to heic free',
  alternates: {
    canonical: `${baseUrl}/jpg-to-heic`,
  },
  openGraph: {
    title: 'The Best Free JPG to HEIC Converter Online',
    description: 'Convert JPG to HEIC online for free. Fast batch conversion with no watermarks. Save up to 50% storage space with superior HEIC compression.',
    url: `${baseUrl}/jpg-to-heic`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/jpg-to-heic-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free JPG to HEIC Converter Online',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free JPG to HEIC Converter Online',
    description: 'Convert JPG to HEIC online for free. Fast batch conversion with no watermarks. Save up to 50% storage space with superior HEIC compression.',
    images: [`https://image.heic-tojpg.com/jpg-to-heic-converter-tool.webp`],
  },
};

export default function JpgToHeicLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 