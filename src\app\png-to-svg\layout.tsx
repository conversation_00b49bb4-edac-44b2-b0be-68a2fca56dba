import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for PNG to SVG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free PNG to SVG Converter',
  description: 'Convert PNG to SVG online for free. Fast batch conversion with no watermarks. Transform raster images to scalable vector graphics with high precision.',
  keywords: 'png to svg, convert png to svg, png converter, change png to svg, png to svg free',
  alternates: {
    canonical: `${baseUrl}/png-to-svg`,
  },
  openGraph: {
    title: 'The Best Free PNG to SVG Converter',
    description: 'Convert PNG to SVG online for free. Fast batch conversion with no watermarks. Transform raster images to scalable vector graphics with high precision.',
    url: `${baseUrl}/png-to-svg`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/png-to-svg-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free PNG to SVG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free PNG to SVG Converter',
    description: 'Convert PNG to SVG online for free. Fast batch conversion with no watermarks. Transform raster images to scalable vector graphics with high precision.',
    images: [`https://image.heic-tojpg.com/png-to-svg-converter-tool.webp`],
  },
};

export default function PngToSvgLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 