import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for HEIC to WebP page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free HEIC to WebP Converter',
  description: 'Convert HEIC to WebP online for free. Fast batch conversion with no watermarks. Preserve quality and optimize file size. No installation needed!',
  keywords: 'heic to webp, convert heic to webp, heic to webp converter, heic to webp converter free',
  alternates: {
    canonical: `${baseUrl}/heic-to-webp`,
  },
  openGraph: {
    title: 'The Best Free HEIC to WebP Converter',
    description: 'Convert HEIC to WebP online for free. Fast batch conversion with no watermarks. Preserve quality and optimize file size. No installation needed!',
    url: `${baseUrl}/heic-to-webp`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/heic-to-webp-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free HEIC to WebP Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free HEIC to WebP Converter',
    description: 'Convert HEIC to WebP online for free. Fast batch conversion with no watermarks. Preserve quality and optimize file size. No installation needed!',
    images: [`https://image.heic-tojpg.com/heic-to-webp-converter-tool.webp`],
  },
};

export default function HeicToWebpLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 