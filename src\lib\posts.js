import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { remark } from 'remark'
import html from 'remark-html'

// 定义存储 Markdown 文件的目录路径
const postsDirectory = path.join(process.cwd(), 'data', 'md')

// 获取排序后的文章数据列表
export function getSortedPostsData() {
  // 读取 /data/md 目录下的所有文件名
  const fileNames = fs.readdirSync(postsDirectory)
  const allPostsData = fileNames.map(fileName => {
    // 去掉文件名中的 ".md" 后缀，得到 slug
    const slug = fileName.replace(/\.md$/, '')

    // 读取 markdown 文件内容
    const fullPath = path.join(postsDirectory, fileName)
    const fileContents = fs.readFileSync(fullPath, 'utf8')

    // 使用 gray-matter 解析文章元数据
    const matterResult = matter(fileContents)

    // 将数据与 slug 组合
    return {
      slug,
      ...matterResult.data
    }
  })
  // 按日期排序文章
  return allPostsData.sort((a, b) => {
    if (a.date < b.date) {
      return 1
    } else {
      return -1
    }
  })
}

// 获取单篇文章的数据
export async function getPostData(slug) {
  if (!slug) {
    throw new Error('Slug is undefined');
  }

  const fullPath = path.join(postsDirectory, `${slug}.md`);
  
  try {
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const matterResult = matter(fileContents);

    const processedContent = await remark()
      .use(html)
      .process(matterResult.content);
    const contentHtml = processedContent.toString();

    return {
      slug,
      contentHtml,
      title: matterResult.data.title,
      description: matterResult.data.description,
      date: matterResult.data.date,
      ...matterResult.data
    };
  } catch (error) {
    console.error(`Error reading file for slug "${slug}":`, error);
    throw new Error(`Failed to get post data for slug "${slug}"`);
  }
}
// 获取所有文章的 slugs
export function getAllPostSlugs() {
  const fileNames = fs.readdirSync(postsDirectory)
  return fileNames.map(fileName => {
    return {
      params: {
        slug: fileName.replace(/\.md$/, '')
      }
    }
  })
}