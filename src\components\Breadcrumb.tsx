'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FiChevronRight, FiHome } from 'react-icons/fi';
import { useEffect, useState } from 'react';

interface BreadcrumbItem {
  label: string;
  path: string;
}

export function Breadcrumb() {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // 检查是否应该显示面包屑
  const shouldShowBreadcrumb = () => {
    // 首页不显示面包屑
    if (pathname === '/') return false;

    // 所有其他页面都显示面包屑
    return true;
  };

  // 根据路径生成面包屑项
  const getBreadcrumbs = (): BreadcrumbItem[] => {
    const paths = pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Home', path: '/' }
    ];

    let currentPath = '';
    paths.forEach((path, index) => {
      currentPath += `/${path}`;
      
      // 特殊处理文章页面的面包屑
      if (path === 'posts') {
        breadcrumbs.push({
          label: 'Blog',
          path: '/posts'
        });
        // 如果是文章列表页，到这里就结束
        if (pathname === '/posts') return;
      } else if (paths[0] === 'posts' && index === 1) {
        // 对于单篇文章，使用文章标题作为标签
        breadcrumbs.push({
          label: path.split('-').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
          ).join(' '),
          path: currentPath
        });
      } else {
        // 常规页面的处理
        const pageLabels: { [key: string]: string } = {
          'privacy': 'Privacy Policy',
          'terms': 'Terms of Service',
          'about': 'About Us',
          'contact': 'Contact',
        };

        breadcrumbs.push({
          label: pageLabels[path] || path.split('-').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
          ).join(' '),
          path: currentPath
        });
      }
    });

    return breadcrumbs;
  };

  // 如果组件还没有挂载，返回 null
  if (!mounted) {
    return null;
  }

  // 检查是否应该显示面包屑
  if (!shouldShowBreadcrumb()) {
    return null;
  }

  const breadcrumbs = getBreadcrumbs();

  return (
    <>
      {/* 可见的面包屑导航 */}
      <nav aria-label="Breadcrumb" className="max-w-4xl mx-auto px-4 py-3 bg-white/50 backdrop-blur-sm border-b border-gray-100 mb-6">
        <ol className="flex items-center space-x-2 text-sm">
          {breadcrumbs.map((item, index) => (
            <li key={item.path} className="flex items-center">
              {index > 0 && (
                <FiChevronRight className="mx-2 text-gray-400 flex-shrink-0" aria-hidden="true" />
              )}
              {index === breadcrumbs.length - 1 ? (
                <span className="text-gray-800 font-medium" aria-current="page">
                  {item.label}
                </span>
              ) : (
                <Link 
                  href={item.path}
                  className="text-indigo-600 hover:text-indigo-800 flex items-center transition-colors duration-200"
                >
                  {index === 0 ? (
                    <>
                      <FiHome className="mr-1 flex-shrink-0" />
                      <span className="sr-only">{item.label}</span>
                    </>
                  ) : (
                    item.label
                  )}
                </Link>
              )}
            </li>
          ))}
        </ol>
        {/* Debug info */}
        {process.env.NODE_ENV === 'development' && (
          <div className="text-xs text-gray-400 mt-1">
            Current path: {pathname}
          </div>
        )}
      </nav>

      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'BreadcrumbList',
            'itemListElement': breadcrumbs.map((item, index) => ({
              '@type': 'ListItem',
              'position': index + 1,
              'item': {
                '@id': `https://heic-tojpg.com${item.path}`,
                'name': item.label
              }
            }))
          })
        }}
      />
    </>
  );
} 