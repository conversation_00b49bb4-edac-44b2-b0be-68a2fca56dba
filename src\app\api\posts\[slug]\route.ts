import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { marked } from 'marked';

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const postsDirectory = path.join(process.cwd(), 'data/md');
    const fullPath = path.join(postsDirectory, `${params.slug}.md`);

    // 检查文件是否存在
    if (!fs.existsSync(fullPath)) {
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      );
    }

    // 读取文件内容
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    
    // 使用 gray-matter 解析 markdown 文件
    const { data, content } = matter(fileContents);
    
    // 将 markdown 转换为 HTML
    const htmlContent = marked(content);

    // 返回文章数据
    return NextResponse.json({
      title: data.title || '',
      description: data.description || '',
      slug: data.slug || params.slug,
      thumbnail: data.thumbnail || '',
      date: data.date || '',
      content: htmlContent
    });

  } catch (error) {
    console.error('Error loading post:', error);
    return NextResponse.json(
      { error: 'Failed to load post' },
      { status: 500 }
    );
  }
} 