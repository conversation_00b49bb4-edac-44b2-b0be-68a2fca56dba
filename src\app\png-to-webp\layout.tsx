import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for PNG to WebP page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free PNG to WebP Converter',
  description: 'Convert PNG to WebP online for free. Fast batch conversion with superior compression. Reduce file size by up to 80% while preserving quality and transparency.',
  keywords: 'png to webp, convert png to webp, png to webp converter, png to webp free, converter png to webp',
  alternates: {
    canonical: `${baseUrl}/png-to-webp`,
  },
  openGraph: {
    title: 'The Best Free PNG to WebP Converter',
    description: 'Convert PNG to WebP online for free. Fast batch conversion with superior compression. Reduce file size by up to 80% while preserving quality and transparency.',
    url: `${baseUrl}/png-to-webp`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/png-to-webp-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free PNG to WebP Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free PNG to WebP Converter',
    description: 'Convert PNG to WebP online for free. Fast batch conversion with superior compression. Reduce file size by up to 80% while preserving quality and transparency.',
    images: [`https://image.heic-tojpg.com/png-to-webp-converter-tool.webp`],
  },
};

export default function PngToWebpLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 