import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for AVIF to JPG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free AVIF to JPG Converter Online',
  description: 'Convert AVIF to JPG online for free. Fast batch conversion with high quality output. No watermarks, no registration. Convert .AVIF files to JPG format easily!',
  keywords: 'avif to jpg, convert avif to jpg, .avif to jpg, avif to jpg converter, avif file to jpg',
  alternates: {
    canonical: `${baseUrl}/avif-to-jpg`,
  },
  openGraph: {
    title: 'The Best Free AVIF to JPG Converter Online',
    description: 'Convert AVIF to JPG online for free. Fast batch conversion with high quality output. No watermarks, no registration. Convert .AVIF files to JPG format easily!',
    url: `${baseUrl}/avif-to-jpg`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/avif-to-jpg-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free AVIF to JPG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free AVIF to JPG Converter Online',
    description: 'Convert AVIF to JPG online for free. Fast batch conversion with high quality output. No watermarks, no registration. Convert .AVIF files to JPG format easily!',
    images: [`https://image.heic-tojpg.com/avif-to-jpg-converter-tool.webp`],
  },
};

export default function AvifToJpgLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 