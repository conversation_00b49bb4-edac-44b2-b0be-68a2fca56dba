import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for PNG to JPG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free PNG to JPG Converter',
  description: 'Convert PNG to JPG online for free. Fast batch conversion with high-quality results. Transform PNG to JPG format without watermarks or limits.',
  keywords: 'png to jpg, convert png to jpg, png to jpg converter, change png to jpg, turn png to jpg',
  alternates: {
    canonical: `${baseUrl}/png-to-jpg`,
  },
  openGraph: {
    title: 'The Best Free PNG to JPG Converter',
    description: 'Convert PNG to JPG online for free. Fast batch conversion with high-quality results. Transform PNG to JPG format without watermarks or limits.',
    url: `${baseUrl}/png-to-jpg`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/png-to-jpg-converter.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free PNG to JPG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free PNG to JPG Converter',
    description: 'Convert PNG to JPG online for free. Fast batch conversion with high-quality results. Transform PNG to JPG format without watermarks or limits.',
    images: [`https://image.heic-tojpg.com/png-to-jpg-converter.webp`],
  },
};

export default function PngToJpgLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 