import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import crypto from 'crypto';

// 生成安全的文件名
function generateSafeFileName(originalName: string): string {
  // 生成8位随机字符串
  const randomString = crypto.randomBytes(4).toString('hex');
  // 获取时间戳
  const timestamp = Date.now();
  // 提取原始文件名中的非特殊字符（可选）
  const safeOriginalName = originalName
    .replace(/[^a-zA-Z0-9]/g, '') // 只保留字母和数字
    .slice(0, 20); // 限制长度

  return `${timestamp}-${randomString}${safeOriginalName ? '-' + safeOriginalName : ''}.jpg`;
}

// 检查文件是否为SVG
function isSupportedFormat(fileName: string): boolean {
  const lowerName = fileName.toLowerCase();
  return lowerName.endsWith('.svg');
}

// 初始化 S3 客户端 (Cloudflare R2)
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const quality = parseInt(formData.get('quality') as string) || 85;
    const removeExif = formData.get('removeExif') === 'true';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // 检查文件类型
    if (!isSupportedFormat(file.name)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only SVG files are supported.' },
        { status: 400 }
      );
    }

    // 读取文件内容
    let buffer;
    try {
      buffer = Buffer.from(await file.arrayBuffer());
    } catch (error) {
      console.error('Error reading file:', error);
      return NextResponse.json(
        { error: 'Failed to read file' },
        { status: 500 }
      );
    }
    
    // 处理SVG文件转换为JPG
    let outputBuffer;
    try {
      // 设置SVG转换选项以防止黑色背景问题
      // Convert SVG to JPG with white background
      outputBuffer = await sharp(buffer, {
        // 确保SVG可以正确渲染
        density: 300 // 设置更高的DPI以获得更好的质量
      })
        .flatten({ background: { r: 255, g: 255, b: 255 } }) // 确保背景是白色
        .jpeg({
          quality: quality,
          chromaSubsampling: '4:4:4',  // No chroma subsampling for better quality
          mozjpeg: true                // Use mozjpeg for better compression
        })
        .toBuffer();
    } catch (error) {
      console.error('Error converting SVG to JPG:', error);
      return NextResponse.json(
        { error: 'Failed to convert SVG to JPG' },
        { status: 500 }
      );
    }

    // 移除EXIF信息（如果需要）
    let finalOutput = outputBuffer;
    if (removeExif) {
      try {
        finalOutput = await sharp(outputBuffer)
          .withMetadata({ exif: {} })
          .toBuffer();
      } catch (error) {
        console.error('Error removing EXIF:', error);
        // 继续使用原始输出，不中断流程
        finalOutput = outputBuffer;
      }
    }

    // 生成安全的文件名
    const fileName = generateSafeFileName(file.name);

    // 上传到Cloudflare R2
    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
          Key: fileName,
          Body: finalOutput,
          ContentType: 'image/jpeg',
          // 添加原始文件名作为元数据
          Metadata: {
            originalName: encodeURIComponent(file.name),
            exifRemoved: removeExif.toString()
          }
        })
      );
    } catch (error) {
      console.error('Error uploading to R2:', error);
      return NextResponse.json(
        { error: 'Failed to upload converted file' },
        { status: 500 }
      );
    }

    // 返回下载API的URL
    const downloadUrl = `/api/download?key=${encodeURIComponent(fileName)}`;

    return NextResponse.json({
      success: true,
      url: downloadUrl,
      originalName: file.name
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}