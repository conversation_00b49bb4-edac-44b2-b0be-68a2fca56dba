import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for JPEG to JPG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free JPEG to JPG Converter Online',
  description: 'Convert JPEG to JPG online for free. Fast batch conversion with no quality loss. Preserve metadata or remove EXIF data for privacy. No installation needed!',
  keywords: 'jpeg to jpg, conversion of jpeg to jpg, convert jpeg to jpg, jpeg to jpg converter',
  alternates: {
    canonical: `${baseUrl}/jpeg-to-jpg`,
  },
  openGraph: {
    title: 'The Best Free JPEG to JPG Converter Online',
    description: 'Convert JPEG to JPG online for free. Fast batch conversion with no quality loss. Preserve metadata or remove EXIF data for privacy. No installation needed!',
    url: `${baseUrl}/jpeg-to-jpg`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/jpeg-to-jpg-converter.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free JPEG to JPG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free JPEG to JPG Converter Online',
    description: 'Convert JPEG to JPG online for free. Fast batch conversion with no quality loss. Preserve metadata or remove EXIF data for privacy. No installation needed!',
    images: [`https://image.heic-tojpg.com/jpeg-to-jpg-converter.webp`],
  },
};

export default function JpegToJpgLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 