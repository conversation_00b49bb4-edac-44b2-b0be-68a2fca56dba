import React from 'react';
import Link from 'next/link';

interface RelatedToolsProps {
  tools?: string[];
  currentTool?: string;
}

const allTools = [
  { name: 'HEIC to JPG', path: '/' },
  { name: 'WebP to PNG', path: '/webp-to-png' },
  { name: 'WebP to JPG', path: '/webp-to-jpg' },
  { name: 'JPG to PDF', path: '/jpg-to-pdf' },
  { name: 'JPG to PNG', path: '/jpg-to-png' },
  { name: 'JPG to JPEG', path: '/jpg-to-jpeg' },
  { name: 'JPG to SVG', path: '/jpg-to-svg' },
  { name: 'HEIC to GIF', path: '/heic-to-gif' },
  { name: 'JPEG to JPG', path: '/jpeg-to-jpg' },
  { name: 'SVG to JPG', path: '/svg-to-jpg' },
  { name: 'JPG to HEIC', path: '/jpg-to-heic' },
  { name: 'JPG to WebP', path: '/jpg-to-webp' },
  { name: 'PNG to WebP', path: '/png-to-webp' },
  { name: 'PNG to JPG', path: '/png-to-jpg' },
  { name: 'JFIF to JPG', path: '/jfif-to-jpg' },
  { name: 'JFIF to PNG', path: '/jfif-to-png' },
  { name: 'JPEG to JPG', path: '/jpeg-to-jpg' },
  { name: 'AVIF to JPG', path: '/avif-to-jpg' },
  { name: 'AVIF to PNG', path: '/avif-to-png' },
  { name: 'AVIF to WebP', path: '/avif-to-webp' },
  { name: 'SVG to JPG', path: '/svg-to-jpg' },
  { name: 'SVG to PNG', path: '/svg-to-png' },
  { name: 'WebP to AVIF', path: '/webp-to-avif' },
  { name: 'WebP to GIF', path: '/webp-to-gif' },
  { name: 'JPG to AVIF', path: '/jpg-to-avif' },
  { name: 'HEIC to PNG', path: '/heic-to-png' },
  { name: 'HEIC to WebP', path: '/heic-to-webp' },
  { name: 'HEIC to GIF', path: '/heic-to-gif' },
  { name: 'HEIC to AVIF', path: '/heic-to-avif' },
  { name: 'GIF to AVIF', path: '/gif-to-avif' },
  { name: 'PNG to SVG', path: '/png-to-svg' },
  { name: 'JPG to HEIC', path: '/jpg-to-heic' },
];

const RelatedTools: React.FC<RelatedToolsProps> = ({ tools, currentTool }) => {
  // 如果提供了特定的工具列表，则使用它，否则使用除当前工具外的所有工具
  const displayTools = tools || 
    allTools.filter(tool => tool.name !== currentTool);
  
  // 如果提供了tools参数，则直接使用；否则，从allTools中查找对应的工具对象
  const toolsToDisplay = Array.isArray(displayTools) 
    ? displayTools.map(toolName => {
        if (typeof toolName === 'string') {
          const found = allTools.find(t => t.name === toolName);
          return found || { name: toolName, path: '/' };
        }
        return toolName;
      })
    : displayTools;

  return (
    <div className="mt-12 p-4 bg-gradient-to-br from-slate-50/90 via-white to-white rounded-lg shadow-md border border-gray-100">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Related Tools</h2>
      <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-3 md:gap-4">
        {toolsToDisplay.map((tool, index) => (
          <Link 
            key={index} 
            href={tool.path}
            className="p-2 md:p-3 bg-white rounded-lg border border-gray-200 hover:border-indigo-300 hover:shadow-md transition-all duration-200 text-center text-sm md:text-base"
          >
            {tool.name}
          </Link>
        ))}
      </div>
    </div>
  );
};

export default RelatedTools; 