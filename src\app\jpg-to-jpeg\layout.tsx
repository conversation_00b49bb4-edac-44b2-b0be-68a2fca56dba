import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for JPG to JPEG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free JPG to JPEG Converter Online',
  description: 'Convert JPG to JPEG online for free. Fast batch conversion with no watermarks. Transfer JPG to JPEG format with professional quality. No installation needed!',
  keywords: 'jpg to jpeg, jpg to jpeg converter, transfer jpg to jpeg, jpg to jpeg format converter',
  alternates: {
    canonical: `${baseUrl}/jpg-to-jpeg`,
  },
  openGraph: {
    title: 'The Best Free JPG to JPEG Converter Online',
    description: 'Convert JPG to JPEG online for free. Fast batch conversion with no watermarks. Transfer JPG to JPEG format with professional quality. No installation needed!',
    url: `${baseUrl}/jpg-to-jpeg`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/jpg-to-jpeg-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free JPG to JPEG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free JPG to JPEG Converter Online',
    description: 'Convert JPG to JPEG online for free. Fast batch conversion with no watermarks. Transfer JPG to JPEG format with professional quality. No installation needed!',
    images: [`https://image.heic-tojpg.com/jpg-to-jpeg-converter-tool.webp`],
  },
};

export default function JpgToJpegLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 