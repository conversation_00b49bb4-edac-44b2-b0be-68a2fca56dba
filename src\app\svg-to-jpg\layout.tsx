import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for SVG to JPG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free SVG to JPG Converter online',
  description: 'Convert SVG to JPG online for free. Fast vector-to-raster conversion with no watermarks. Preserve quality with advanced rendering. No installation needed!',
  keywords: 'svg to jpg, svg converter, convert svg, svg converter to jpg, converting svg to jpg',
  alternates: {
    canonical: `${baseUrl}/svg-to-jpg`,
  },
  openGraph: {
    title: 'The Best Free SVG to JPG Converter online',
    description: 'Convert SVG to JPG online for free. Fast vector-to-raster conversion with no watermarks. Preserve quality with advanced rendering. No installation needed!',
    url: `${baseUrl}/svg-to-jpg`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/svg-to-jpg-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free SVG to JPG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free SVG to JPG Converter online',
    description: 'Convert SVG to JPG online for free. Fast vector-to-raster conversion with no watermarks. Preserve quality with advanced rendering. No installation needed!',
    images: [`https://image.heic-tojpg.com/svg-to-jpg-converter-tool.webp`],
  },
};

export default function SvgToJpgLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 