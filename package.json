{"name": "heic-to-jpg-converter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.0.0", "@octokit/rest": "^20.0.2", "@radix-ui/react-slot": "^1.0.2", "@types/node": "20.11.25", "@types/pdfkit": "^0.14.0", "@types/potrace": "^2.1.5", "@types/react": "18.2.64", "@types/react-dom": "18.2.21", "autoprefixer": "10.4.18", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "eslint": "8.57.0", "eslint-config-next": "14.1.3", "gray-matter": "^4.0.3", "heic-convert": "^2.0.0", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "marked": "^15.0.7", "next": "14.1.3", "pdf-lib": "^1.17.1", "pdf2pic": "^3.2.0", "pdfkit": "^0.17.1", "postcss": "8.4.35", "potrace": "^2.1.8", "react": "18.2.0", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-icons": "^5.0.1", "sharp": "^0.33.2", "tailwind-merge": "^2.2.1", "tailwindcss": "3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "5.4.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16"}}