import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import os from 'os';
import { exec } from 'child_process';
import { promisify } from 'util';

const execPromise = promisify(exec);

// Generate safe file name
function generateSafeFileName(originalName: string): string {
  // Generate 8-char random string
  const randomString = crypto.randomBytes(4).toString('hex');
  // Get timestamp
  const timestamp = Date.now();
  // Extract non-special characters from original filename
  const safeOriginalName = originalName
    .replace(/[^a-zA-Z0-9]/g, '') // Only keep letters and numbers
    .slice(0, 20); // Limit length

  return `${timestamp}-${randomString}${safeOriginalName ? '-' + safeOriginalName : ''}.png`;
}

// Check if file is supported AVIF format
function isSupportedFormat(filename: string): boolean {
  return /\.(avif|AVIF)$/i.test(filename);
}

// Check if FFmpeg is available
async function isFFmpegAvailable(): Promise<boolean> {
  try {
    await execPromise('ffmpeg -version');
    return true;
  } catch (error) {
    console.warn('FFmpeg is not available:', error);
    return false;
  }
}

// Initialize S3 client (Cloudflare R2)
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

// Convert AVIF to PNG using FFmpeg
async function convertWithFFmpeg(inputFilePath: string): Promise<Buffer> {
  const tempDir = os.tmpdir();
  const outputPath = path.join(tempDir, `output-${Date.now()}.png`);
  
  try {
    // Use FFmpeg for conversion with high quality settings
    const ffmpegCmd = `ffmpeg -i "${inputFilePath}" -compression_level 0 "${outputPath}"`;
    console.log(`Running FFmpeg command: ${ffmpegCmd}`);
    
    const { stdout, stderr } = await execPromise(ffmpegCmd);
    if (stderr) {
      console.log('FFmpeg stderr:', stderr);
    }
    
    // Read output file
    const outputBuffer = fs.readFileSync(outputPath);
    
    // Clean up temp files
    fs.unlinkSync(outputPath);
    
    return outputBuffer;
  } catch (error) {
    // Ensure temp files are cleaned up
    try {
      if (fs.existsSync(outputPath)) fs.unlinkSync(outputPath);
    } catch (cleanupError) {
      console.error('Error cleaning up temp files:', cleanupError);
    }
    
    throw error;
  }
}

export async function POST(request: NextRequest) {
  // Create temporary file path for FFmpeg
  const tempDir = os.tmpdir();
  const tempInputPath = path.join(tempDir, `input-${Date.now()}.avif`);
  let needCleanup = false;
  
  // Check if ffmpeg is available
  const ffmpegAvailable = await isFFmpegAvailable();
  
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const quality = parseInt(formData.get('quality') as string) || 85;
    const removeExif = formData.get('removeExif') === 'true';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file type
    if (!isSupportedFormat(file.name)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only AVIF files are supported.' },
        { status: 400 }
      );
    }

    // Read file content
    let buffer;
    try {
      buffer = Buffer.from(await file.arrayBuffer());
      
      // Write buffer to temp file for FFmpeg
      fs.writeFileSync(tempInputPath, new Uint8Array(buffer));
      needCleanup = true;
    } catch (error) {
      console.error('Error reading file:', error);
      return NextResponse.json(
        { error: 'Failed to read file' },
        { status: 500 }
      );
    }
    
    // Process AVIF file conversion to PNG
    let optimizedOutput;
    
    // Try FFmpeg conversion if available
    if (ffmpegAvailable) {
      try {
        console.log('Attempting conversion with FFmpeg...');
        optimizedOutput = await convertWithFFmpeg(tempInputPath);
        console.log('FFmpeg conversion successful');
      } catch (ffmpegError) {
        console.error('FFmpeg conversion failed:', ffmpegError);
        // If FFmpeg fails, continue with other methods
        optimizedOutput = null;
      }
    } else {
      console.log('FFmpeg not available, skipping FFmpeg conversion');
      optimizedOutput = null;
    }
    
    // If FFmpeg conversion fails or unavailable, try Sharp method
    if (!optimizedOutput) {
      try {
        // Method 1: Direct conversion
        console.log('Attempting conversion with Sharp method 1...');
        let sharpInstance = sharp(buffer);
        
        // Set output to PNG format
        sharpInstance = sharpInstance.png({
          compressionLevel: Math.min(9, Math.max(0, Math.floor((quality / 100) * 9))), // 0-9, higher means more compression
          quality: quality,
        });
        
        // Remove EXIF if requested
        if (removeExif) {
          sharpInstance = sharpInstance.withMetadata({
            exif: {
              IFD0: {
                Copyright: 'Processed by heic-tojpg.com',
                Software: 'heic-tojpg.com'
              }
            }
          });
        } else {
          // Preserve color profiles
          sharpInstance = sharpInstance.withMetadata();
        }

        optimizedOutput = await sharpInstance.toBuffer();
        console.log('Sharp method 1 conversion successful');
      } catch (conversionError) {
        console.error('Sharp method 1 failed:', conversionError);
        
        try {
          // Method 2: Simplified conversion path
          console.log('Attempting conversion with Sharp method 2...');
          let sharpInstance = sharp(buffer, { failOn: 'none' });
          
          // Direct PNG conversion with quality settings
          optimizedOutput = await sharpInstance
            .png({ 
              compressionLevel: Math.min(9, Math.max(0, Math.floor((quality / 100) * 9)))
            })
            .toBuffer();
          console.log('Sharp method 2 conversion successful');
        } catch (fallbackError) {
          console.error('Sharp method 2 failed:', fallbackError);
          
          try {
            // Method 3: Using libvips direct conversion options
            console.log('Attempting conversion with Sharp method 3...');
            let sharpInstance = sharp(buffer, { 
              failOn: 'none',
              limitInputPixels: false,
              sequentialRead: true
            });
            
            // Try direct conversion to PNG
            optimizedOutput = await sharpInstance
              .toFormat('png')
              .toBuffer();
            console.log('Sharp method 3 conversion successful');
          } catch (method3Error) {
            console.error('Sharp method 3 failed:', method3Error);
            
            try {
              // Fallback method: Create an error image
              console.log('All conversion methods failed, creating error image...');
              
              // Create a simple red error image
              const errorImage = await sharp({
                create: {
                  width: 800,
                  height: 600,
                  channels: 3,
                  background: { r: 255, g: 0, b: 0 }
                }
              })
              .png()
              .toBuffer();
              
              // Generate error file name
              const errorFileName = `error-${Date.now()}-${crypto.randomBytes(4).toString('hex')}.png`;
              
              // Upload error image
              try {
                await s3Client.send(
                  new PutObjectCommand({
                    Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
                    Key: errorFileName,
                    Body: errorImage,
                    ContentType: 'image/png',
                    Metadata: {
                      originalName: encodeURIComponent(file.name),
                      isErrorImage: 'true'
                    }
                  })
                );
                
                // Return special error response with the error image URL
                const errorImageUrl = `/api/download?key=${encodeURIComponent(errorFileName)}`;
                return NextResponse.json({
                  success: false,
                  errorImage: true,
                  url: errorImageUrl,
                  error: 'Failed to convert AVIF to PNG'
                });
                
              } catch (uploadError) {
                console.error('Error uploading error image to R2:', uploadError);
                throw new Error('Failed to upload error image');
              }
              
            } catch (fallbackMethodError) {
              console.error('Fallback method failed:', fallbackMethodError);
              throw new Error('All conversion methods failed');
            }
          }
        }
      }
    }

    // If all methods fail, return error
    if (!optimizedOutput) {
      return NextResponse.json(
        { 
          error: 'Failed to convert AVIF to PNG',
          details: 'All conversion methods failed. The AVIF format might be using features not supported by the converter. Please try a different AVIF file.'
        },
        { status: 500 }
      );
    }

    // Generate safe file name
    const fileName = generateSafeFileName(file.name);

    // Upload to Cloudflare R2
    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
          Key: fileName,
          Body: optimizedOutput,
          ContentType: 'image/png',
          // Add original filename as metadata
          Metadata: {
            originalName: encodeURIComponent(file.name),
            exifRemoved: removeExif.toString()
          }
        })
      );
    } catch (error) {
      console.error('Error uploading to R2:', error);
      return NextResponse.json(
        { error: 'Failed to upload converted file' },
        { status: 500 }
      );
    }

    // Return download API URL
    const downloadUrl = `/api/download?key=${encodeURIComponent(fileName)}`;

    // Return success response with proper headers to prevent page refresh
    return new NextResponse(
      JSON.stringify({
        success: true,
        url: downloadUrl,
        originalName: file.name
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-store'
        }
      }
    );

  } catch (error: any) {
    console.error('Error in AVIF to PNG conversion:', error);
    
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  } finally {
    // Clean up temp files
    if (needCleanup) {
      try {
        if (fs.existsSync(tempInputPath)) {
          fs.unlinkSync(tempInputPath);
        }
      } catch (cleanupError) {
        console.error('Error cleaning up temp input file:', cleanupError);
      }
    }
  }
} 