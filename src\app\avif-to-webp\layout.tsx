import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for AVIF to WebP page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free AVIF to WebP Converter',
  description: 'Convert AVIF to WebP online for free. Fast batch conversion with no watermarks. Preserve quality and transparency.',
  keywords: 'avif to webp, convert avif to webp, avif to webp free, avif converter, online converter',
  alternates: {
    canonical: `${baseUrl}/avif-to-webp`,
  },
  openGraph: {
    title: 'The Best Free AVIF to WebP Converter',
    description: 'Convert AVIF to WebP online for free. Fast batch conversion with no watermarks. Preserve quality and transparency.',
    url: `${baseUrl}/avif-to-webp`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/avif-to-webp-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free AVIF to WebP Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free AVIF to WebP Converter',
    description: 'Convert AVIF to WebP online for free. Fast batch conversion with no watermarks. Preserve quality and transparency.',
    images: [`https://image.heic-tojpg.com/avif-to-webp-converter-tool.webp`],
  },
};

export default function AvifToWebpLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 