import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import crypto from 'crypto';
import { jsPDF } from 'jspdf';

// 生成安全的文件名
function generateSafeFileName(originalName: string): string {
  // 生成8位随机字符串
  const randomString = crypto.randomBytes(4).toString('hex');
  // 获取时间戳
  const timestamp = Date.now();
  // 提取原始文件名中的非特殊字符（可选）
  const safeOriginalName = originalName
    .replace(/[^a-zA-Z0-9]/g, '') // 只保留字母和数字
    .slice(0, 20); // 限制长度

  return `${timestamp}-${randomString}${safeOriginalName ? '-' + safeOriginalName : ''}.pdf`;
}

// 检查文件是否为支持的格式
function isSupportedFormat(fileName: string): boolean {
  const lowerName = fileName.toLowerCase();
  return lowerName.endsWith('.jpg') || lowerName.endsWith('.jpeg');
}

// 初始化 S3 客户端 (Cloudflare R2)
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const quality = parseInt(formData.get('quality') as string) || 85;
    const removeExif = formData.get('removeExif') === 'true';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // 检查文件类型
    if (!file.type && !isSupportedFormat(file.name)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPG and JPEG files are supported.' },
        { status: 400 }
      );
    }

    // 读取文件内容
    let buffer;
    try {
      buffer = Buffer.from(await file.arrayBuffer());
    } catch (error) {
      console.error('Error reading file:', error);
      return NextResponse.json(
        { error: 'Failed to read file' },
        { status: 500 }
      );
    }
    
    // 处理JPG图片
    let imageBuffer;
    try {
      let sharpInstance = sharp(buffer);
      
      // 如果需要移除EXIF信息
      if (removeExif) {
        sharpInstance = sharpInstance.withMetadata({
          exif: {
            IFD0: {
              Copyright: 'Processed by heic-tojpg.com',
              Software: 'heic-tojpg.com'
            }
          }
        });
      } else {
        // 保留颜色配置
        sharpInstance = sharpInstance.withMetadata();
      }

      // 优化图像质量
      imageBuffer = await sharpInstance
        .jpeg({ quality: quality, mozjpeg: true })
        .toBuffer();
      
    } catch (error) {
      console.error('Error processing image:', error);
      return NextResponse.json(
        { error: 'Failed to process image. Please make sure the file is valid.' },
        { status: 500 }
      );
    }

    // 获取图像尺寸
    const imageMetadata = await sharp(imageBuffer).metadata();
    const { width = 0, height = 0 } = imageMetadata;

    // 创建PDF
    let pdfBuffer: Buffer;
    try {
      // 创建base64编码的图片数据
      const imageBase64 = imageBuffer.toString('base64');
      const imageDataURL = `data:image/jpeg;base64,${imageBase64}`;
      
      // 计算PDF页面大小，确保图片适合页面
      // jsPDF使用点(pt)作为单位，1点 = 1/72英寸
      // 我们将图像大小转换为PDF适合的大小
      const pdfWidth = width / 2.83; // 将像素转换为点 (72/25.4)
      const pdfHeight = height / 2.83;
      
      // 创建PDF文档
      const doc = new jsPDF({
        orientation: pdfWidth > pdfHeight ? 'landscape' : 'portrait',
        unit: 'pt',
        format: [pdfWidth, pdfHeight]
      });
      
      // 添加图片到PDF
      doc.addImage(imageDataURL, 'JPEG', 0, 0, pdfWidth, pdfHeight);
      
      // 获取PDF二进制数据
      pdfBuffer = Buffer.from(doc.output('arraybuffer'));
      
    } catch (error) {
      console.error('Error creating PDF:', error);
      return NextResponse.json(
        { error: 'Failed to create PDF' },
        { status: 500 }
      );
    }

    // 生成安全的文件名
    const fileName = generateSafeFileName(file.name);

    // 上传到Cloudflare R2
    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
          Key: fileName,
          Body: pdfBuffer,
          ContentType: 'application/pdf',
          // 添加原始文件名作为元数据
          Metadata: {
            originalName: encodeURIComponent(file.name),
            exifRemoved: removeExif.toString()
          }
        })
      );
    } catch (error) {
      console.error('Error uploading to R2:', error);
      return NextResponse.json(
        { error: 'Failed to upload converted file' },
        { status: 500 }
      );
    }

    // 返回下载API的URL
    const downloadUrl = `/api/download?key=${encodeURIComponent(fileName)}`;

    return NextResponse.json({
      success: true,
      url: downloadUrl,
      originalName: file.name
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 