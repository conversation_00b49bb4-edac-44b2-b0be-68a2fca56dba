import { Breadcrumb } from '@/components/Breadcrumb';
import Link from 'next/link';
import RelatedTools from '@/components/RelatedTools';
import { Metadata } from 'next';
import JpgToJpegConverter from '@/components/JpgToJpegConverter';

export const metadata: Metadata = {
  title: 'The Best Free JPG to JPEG Converter Online',
  description: 'Convert JPG to JPEG online for free. Fast batch conversion with no watermarks. Transfer JPG to JPEG format with professional quality. No installation needed!',
  openGraph: {
    title: 'The Best Free JPG to JPEG Converter Online',
    description: 'Convert JPG to JPEG online for free. Fast batch conversion with no watermarks. Transfer JPG to JPEG format with professional quality. No installation needed!',
    url: 'https://heic-tojpg.com/jpg-to-jpeg',
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: 'https://image.heic-tojpg.com/jpg-to-jpeg-converter-tool.webp',
        width: 1200,
        height: 630,
        alt: 'The Best Free JPG to JPEG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  alternates: {
    canonical: 'https://heic-tojpg.com/jpg-to-jpeg',
  },
};

export default function JpgToJpeg() {
  return (
    <>
      <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative bg-gradient-to-b from-slate-50/80 via-white to-white">
        <Breadcrumb />
        <h1 className="text-2xl md:text-4xl font-bold text-center mb-4 md:mb-8 text-gray-800">
          The Best Free JPG to JPEG Converter
        </h1>
        <p className="text-center text-gray-600 mb-6">
          Convert JPG photos to standard JPEG format online
        </p>

        <JpgToJpegConverter />

        {/* Related tools */}
        <RelatedTools currentTool="JPG to JPEG" />

        {/* SEO Content */}
        <div className="mt-12 space-y-8">
          <section>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              About JPG to JPEG Conversion
            </h2>
            <p className="text-gray-600 leading-relaxed">
              JPG and JPEG are essentially the same image format. The difference in file extension is historical - 
              older Windows systems required 3-character extensions (.jpg), while other systems used the full name (.jpeg). 
              Our converter helps standardize your files to the JPEG format while maintaining image quality.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              Why Convert JPG to JPEG?
            </h2>
            <ul className="list-disc list-inside text-gray-600 space-y-2">
              <li>Standardize file extensions across your image library</li>
              <li>Ensure compatibility with systems that specifically require .jpeg extension</li>
              <li>Maintain consistent naming conventions for professional projects</li>
              <li>Optimize file metadata and compression settings</li>
            </ul>
          </section>

          <section>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              Features
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Fast & Efficient</h3>
                <p className="text-gray-600">Convert multiple JPG files to JPEG format quickly with our optimized processing engine.</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Quality Preservation</h3>
                <p className="text-gray-600">Maintain original image quality while standardizing the file format.</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Batch Processing</h3>
                <p className="text-gray-600">Convert up to 100 files at once to save time on large projects.</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Privacy Focused</h3>
                <p className="text-gray-600">All conversions happen securely with automatic file cleanup.</p>
              </div>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              How to Convert JPG to JPEG
            </h2>
            <ol className="list-decimal list-inside text-gray-600 space-y-2">
              <li>Click the upload area or drag and drop your JPG files</li>
              <li>Adjust quality settings if needed (default: 85%)</li>
              <li>Choose whether to remove EXIF data</li>
              <li>Accept the privacy policy</li>
              <li>Click "Convert to JPEG" to start the conversion</li>
              <li>Download your converted JPEG files</li>
            </ol>
          </section>

          <section>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              Technical Details
            </h2>
            <div className="bg-gray-50 p-4 rounded-lg">
              <ul className="text-gray-600 space-y-1 text-sm">
                <li><strong>Supported Input:</strong> JPG files</li>
                <li><strong>Output Format:</strong> JPEG</li>
                <li><strong>Max File Size:</strong> 50MB per file</li>
                <li><strong>Max Files:</strong> 100 files per batch</li>
                <li><strong>Quality Range:</strong> 1-100%</li>
                <li><strong>Processing:</strong> Client-side and server-side</li>
              </ul>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              Frequently Asked Questions
            </h2>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Is there a difference between JPG and JPEG?</h3>
                <p className="text-gray-600">No, JPG and JPEG are the same image format. The only difference is the file extension length.</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Will converting affect image quality?</h3>
                <p className="text-gray-600">No, since JPG and JPEG are the same format, there's no quality loss during conversion.</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Is my data safe?</h3>
                <p className="text-gray-600">Yes, all files are processed securely and automatically deleted after conversion.</p>
              </div>
            </div>
          </section>
        </div>
      </main>
    </>
  );
}
