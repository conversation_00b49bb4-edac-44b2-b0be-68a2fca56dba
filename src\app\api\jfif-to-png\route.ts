import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import crypto from 'crypto';

// Generate secure filename
function generateSafeFileName(originalName: string): string {
  // Generate 8-character random string
  const randomString = crypto.randomBytes(4).toString('hex');
  // Get timestamp
  const timestamp = Date.now();
  // Extract non-special characters from original filename
  const safeOriginalName = originalName
    .replace(/[^a-zA-Z0-9]/g, '') // Keep only letters and numbers
    .slice(0, 20); // Limit length

  return `${timestamp}-${randomString}${safeOriginalName ? '-' + safeOriginalName : ''}.png`;
}

// Check if file is supported JFIF/JPEG format
function isSupportedFormat(fileName: string): boolean {
  const lowerName = fileName.toLowerCase();
  return (
    lowerName.endsWith('.jfif') || 
    lowerName.endsWith('.jpg') ||
    lowerName.endsWith('.jpeg')
  );
}

// Initialize S3 client (Cloudflare R2)
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const quality = parseInt(formData.get('quality') as string) || 85;
    const removeExif = formData.get('removeExif') === 'true';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file type
    if (!isSupportedFormat(file.name)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JFIF, JPG, and JPEG files are supported.' },
        { status: 400 }
      );
    }

    // Read file content
    let buffer;
    try {
      buffer = Buffer.from(await file.arrayBuffer());
    } catch (error) {
      console.error('Error reading file:', error);
      return NextResponse.json(
        { error: 'Failed to read file' },
        { status: 500 }
      );
    }
    
    // Process the JFIF/JPEG file to PNG
    let optimizedOutput;
    try {
      let sharpInstance = sharp(buffer);
      
      // Set output to PNG format
      sharpInstance = sharpInstance.png({
        quality: quality,
        compressionLevel: 9, // Maximum compression
        palette: true, // Use palette-based quantization when quality < 100
      });
      
      // If EXIF information removal is requested
      if (removeExif) {
        sharpInstance = sharpInstance.withMetadata({
          exif: {
            IFD0: {
              Copyright: 'Processed by heic-tojpg.com',
              Software: 'heic-tojpg.com'
            }
          }
        });
      } else {
        // Preserve color profiles
        sharpInstance = sharpInstance.withMetadata(); // Preserves all metadata including ICC profiles
      }

      optimizedOutput = await sharpInstance.toBuffer();
    } catch (error) {
      console.error('Error converting image:', error);
      return NextResponse.json(
        { error: 'Failed to convert image to PNG' },
        { status: 500 }
      );
    }

    // Generate secure filename
    const fileName = generateSafeFileName(file.name);

    // Upload to Cloudflare R2
    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
          Key: fileName,
          Body: optimizedOutput,
          ContentType: 'image/png',
          // Add original filename as metadata
          Metadata: {
            originalName: encodeURIComponent(file.name),
            exifRemoved: removeExif.toString()
          }
        })
      );
    } catch (error) {
      console.error('Error uploading to R2:', error);
      return NextResponse.json(
        { error: 'Failed to upload converted file' },
        { status: 500 }
      );
    }

    // Return download API URL
    const downloadUrl = `/api/download?key=${encodeURIComponent(fileName)}`;

    return NextResponse.json({
      success: true,
      url: downloadUrl,
      originalName: file.name
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 