'use client';

import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { FiUpload, FiImage, FiDownload, FiArrowUp, FiX, FiCheck } from 'react-icons/fi';
import Link from 'next/link';

interface FileItem {
  id: string;
  name: string;
  size: number;
  status: 'waiting' | 'converting' | 'done' | 'error';
  downloadUrl?: string;
  errorMessage?: string;
  file: File;
}

export default function GifToAvifConverter() {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [quality, setQuality] = useState(85);
  const [isConverting, setIsConverting] = useState(false);
  const [removeExif, setRemoveExif] = useState(false);
  const [consentPrivacy, setConsentPrivacy] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [convertProgress, setConvertProgress] = useState({ current: 0, total: 0 });
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Add canonical link
  useEffect(() => {
    let link = document.querySelector('link[rel="canonical"]');
    
    if (!link) {
      link = document.createElement('link');
      link.setAttribute('rel', 'canonical');
      link.setAttribute('href', 'https://heic-tojpg.com/gif-to-avif');
      document.head.appendChild(link);
    } else {
      link.setAttribute('href', 'https://heic-tojpg.com/gif-to-avif');
    }
  }, []);

  // Detect if mobile device after component mount
  useEffect(() => {
    setIsMobile(/mobile|android|ios/i.test(window.navigator.userAgent));
  }, []);

  // Check scroll position to show/hide back to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setError(null);
    
    // Validate file types
    const invalidFiles = acceptedFiles.filter(file => {
      const lowerName = file.name.toLowerCase();
      return !lowerName.endsWith('.gif');
    });
    
    if (invalidFiles.length > 0) {
      setError('Please only upload GIF files.');
      return;
    }

    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(7),
      name: file.name,
      size: file.size,
      status: 'waiting' as const,
      file: file
    }));
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/gif': ['.gif', '.GIF']
    },
    maxFiles: 100,
    noDrag: isMobile,
    onDropRejected: () => {
      setError('Please only upload GIF files.');
    },
    onError: (error) => {
      setError('An error occurred while uploading files.');
      console.error('Dropzone error:', error);
    }
  });

  const handleConvert = async () => {
    if (!consentPrivacy) {
      setError('Please consent to the privacy policy before converting files.');
      return;
    }

    if (files.length === 0) {
      setError('Please add some files to convert.');
      return;
    }

    setError(null);
    setIsConverting(true);
    const filesToConvert = files.filter(f => f.status === 'waiting');
    setConvertProgress({ current: 0, total: filesToConvert.length });
    
    try {
      await Promise.all(filesToConvert.map(async (fileItem, index) => {
        setFiles(prev => 
          prev.map(f => 
            f.id === fileItem.id 
              ? { ...f, status: 'converting' } 
              : f
          )
        );
        setConvertProgress(prev => ({ ...prev, current: index + 1 }));

        try {
          const formData = new FormData();
          formData.append('file', fileItem.file);
          formData.append('quality', quality.toString());
          formData.append('removeExif', removeExif.toString());

          const response = await fetch('/api/gif-to-avif', {
            method: 'POST',
            body: formData,
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Conversion failed');
          }

          setFiles(prev => 
            prev.map(f => 
              f.id === fileItem.id 
                ? { ...f, status: 'done', downloadUrl: result.url } 
                : f
            )
          );
        } catch (error: any) {
          console.error('File conversion error:', error);
          const errorMessage = error.message || 'Failed to convert file';
          setFiles(prev => 
            prev.map(f => 
              f.id === fileItem.id 
                ? { ...f, status: 'error', errorMessage: errorMessage } 
                : f
            )
          );
          setError(`Error converting ${fileItem.name}: ${errorMessage}`);
        }
      }));
    } catch (error: any) {
      console.error('Batch conversion error:', error);
      setError('An error occurred during batch conversion. Please try again.');
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownloadAll = async () => {
    const completedFiles = files.filter(f => f.status === 'done' && f.downloadUrl);
    if (completedFiles.length === 0) {
      setError('No converted files to download');
      return;
    }

    setError(null);
    try {
      // Create a hidden download link to trigger download
      for (const file of completedFiles) {
        const link = document.createElement('a');
        link.href = file.downloadUrl!;
        link.download = file.name.replace('.gif', '').replace('.GIF', '') + '.avif';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        // Add small delay to prevent browser from blocking multiple downloads
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error('Download error:', error);
      setError('Failed to download some files. Please try again.');
    }
  };

  const handleClearAll = () => {
    setFiles([]);
    setError(null);
  };

  const handleDeleteFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
    setError(null);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Scroll to top functionality
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <>
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      <div className="mb-6 md:mb-8 relative">
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-6 md:p-8 text-center cursor-pointer transition-all duration-300 relative backdrop-blur-sm
            ${isDragActive 
              ? 'border-indigo-500 bg-gradient-to-br from-indigo-50/40 to-white/80' 
              : 'border-gray-300 hover:border-indigo-400 hover:bg-gradient-to-br hover:from-slate-50/50 hover:to-white/90'
            } shadow-sm hover:shadow-md`}
        >
          <input {...getInputProps()} accept=".gif,.GIF" />
          <FiImage className="mx-auto text-4xl md:text-5xl mb-3 text-indigo-400" />
          <p className="text-lg md:text-xl mb-2 text-gray-800">Select GIF Images</p>
          <p className="text-sm text-gray-600 mb-2">from your device</p>
          <p className="text-xs text-gray-500">Supports up to 100 files</p>
          
          {isMobile && (
            <div className="mt-4 text-sm text-indigo-600 font-medium">
              Tap here to select photos from your device
            </div>
          )}
        </div>
        {/* Buy me a coffee button outside the dropzone in the left bottom corner */}
        <a
          href="https://ko-fi.com/yourfriendlycreator"
          target="_blank"
          rel="noopener noreferrer"
          className="absolute -bottom-10 left-0 px-2 py-1 md:px-3 md:py-2 text-xs md:text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-all duration-200 flex items-center justify-center"
        >
          <span>☕Buy me a coffee</span>
        </a>
        <div className="absolute -bottom-10 left-[140px] md:left-[160px] text-xs md:text-sm text-gray-600 italic">
          I'll use the money to upgrade to a better server to help you with daily work.
        </div>
      </div>

      {files.length > 0 && (
        <div className="space-y-4 md:space-y-6">
          <div className="bg-white rounded-lg shadow-md overflow-x-auto border border-gray-100">
            <table className="min-w-full table-fixed">
              <thead className="bg-gray-50">
                <tr>
                  <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                  <th className="w-[30%] sm:w-[40%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
                  <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                  <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {files.map((file, index) => (
                  <tr key={file.id}>
                    <td className="w-[10%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                    <td className="w-[30%] sm:w-[40%] px-4 py-3 text-sm font-medium text-gray-900">
                      <div className="truncate" title={file.name}>
                        {isMobile ?
                          file.name.length > 10 ?
                            file.name.slice(0, 7) + '...' + file.name.slice(-3)
                            : file.name
                          : file.name
                        }
                      </div>
                    </td>
                    <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap">
                      {file.status === 'done' && file.downloadUrl ? (
                        <a
                          href={file.downloadUrl}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                          download
                        >
                          Download
                        </a>
                      ) : file.status === 'error' ? (
                        <span className="text-red-500 text-sm" title={file.errorMessage}>Error</span>
                      ) : file.status === 'converting' ? (
                        <span className="text-yellow-500 text-sm">Converting...</span>
                      ) : (
                        <span className="text-gray-500 text-sm">Waiting</span>
                      )}
                    </td>
                    <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatFileSize(file.size)}</td>
                    <td className="w-[10%] px-4 py-3 whitespace-nowrap text-center">
                      {file.status === 'done' ? (
                        <FiCheck className="text-green-500 w-5 h-5 mx-auto" title="Conversion Complete" />
                      ) : (
                        <button
                          onClick={() => handleDeleteFile(file.id)}
                          className="text-gray-500 hover:text-red-600 transition-colors duration-200 focus:outline-none"
                          disabled={file.status === 'converting'}
                          title="Delete File"
                        >
                          <FiX className="w-5 h-5" />
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="bg-gradient-to-br from-slate-50/90 via-white to-white p-4 md:p-6 rounded-lg shadow-md border border-gray-100">
            <h2 className="text-lg md:text-xl font-semibold mb-4 text-gray-800">Output Settings</h2>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Format:</label>
                <select
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  value="AVIF"
                  disabled
                >
                  <option>AVIF</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Quality:</label>
                <div className="flex items-center space-x-2">
                  <input
                    type="range"
                    min="1"
                    max="100"
                    value={quality}
                    onChange={(e) => setQuality(parseInt(e.target.value))}
                    className="flex-1"
                  />
                  <span className="text-sm text-gray-600 w-12">{quality}%</span>
                </div>
              </div>
            </div>

            <div className="mt-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={removeExif}
                  onChange={(e) => setRemoveExif(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Remove all EXIF information</span>
              </label>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                checked={consentPrivacy}
                onChange={(e) => setConsentPrivacy(e.target.checked)}
                className="mt-1 w-5 h-5 md:w-4 md:h-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                required
              />
              <label className="text-sm text-gray-600">
                I consent to heic-tojpg.com collecting and processing my data according to{' '}
                <Link href="/privacy-policy" className="text-indigo-600 hover:text-indigo-800 underline">
                  Privacy Policy
                </Link>
                .
              </label>
            </div>

            <div className="flex flex-col sm:flex-row gap-2 sm:justify-end sm:space-x-3">
              <button
                onClick={handleClearAll}
                className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                disabled={isConverting}
              >
                Clear all
              </button>
              <button
                onClick={handleConvert}
                disabled={isConverting || !files.some(f => f.status === 'waiting') || !consentPrivacy}
                className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {isConverting ? 'Converting...' : 'Convert'}
              </button>

              <button
                onClick={handleDownloadAll}
                disabled={!files.some(f => f.status === 'done')}
                className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 transition-colors duration-200"
              >
                <FiDownload className="w-4 h-4" />
                <span>Download all</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Conversion progress indicator */}
      {isConverting && isMobile && (
        <div className="fixed bottom-4 left-0 right-0 mx-4 bg-gradient-to-r from-indigo-50/90 via-white to-blue-50/90 p-4 rounded-lg shadow-lg border border-indigo-100 backdrop-blur-sm">
          <div className="text-center text-sm text-indigo-800 font-medium">
            Converting: {convertProgress.current} of {convertProgress.total} files
          </div>
          <div className="mt-2 h-2 bg-indigo-100 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-indigo-600 to-indigo-500 transition-all duration-300"
              style={{ width: `${(convertProgress.current / convertProgress.total) * 100}%` }}
            />
          </div>
        </div>
      )}

      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 p-3 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-full shadow-lg hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
          title="Back to top"
        >
          <FiArrowUp className="w-5 h-5" />
        </button>
      )}
    </>
  );
}
