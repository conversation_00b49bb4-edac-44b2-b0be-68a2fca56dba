import React from 'react';

type TitleCaseStyle = 'AP Style' | 'APA Style' | 'Chicago Style' | 'MLA Style' | 'NY Times Style' | 'Wikipedia Style' | 'UPPERCASE' | 'lowercase' | 'Sentence case' | 'Start Case' | 'camelCase' | 'PascalCase' | 'hyphen-case' | 'snake_case' | 'dot.case';

interface StyleSelectorProps {
  selectedStyles: TitleCaseStyle[];
  setSelectedStyles: (styles: TitleCaseStyle[]) => void;
}

const StyleSelector: React.FC<StyleSelectorProps> = ({ selectedStyles, setSelectedStyles }) => {
  const styles: TitleCaseStyle[] = [
    'AP Style', 'APA Style', 'Chicago Style', 'MLA Style', 'NY Times Style', 'Wikipedia Style',
    'UPPERCASE', 'lowercase', 'Sentence case', 'Start Case', 'camelCase', 'PascalCase',
    'hyphen-case', 'snake_case', 'dot.case'
  ];

  const handleChange = (style: TitleCaseStyle) => {
    if (selectedStyles.includes(style)) {
      setSelectedStyles(selectedStyles.filter(s => s !== style));
    } else {
      setSelectedStyles([...selectedStyles, style]);
    }
  };

  return (
    <div className="mb-4">
      <h3 className="text-lg font-semibold mb-2">Select Title Case Styles</h3>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
        {styles.map((style) => (
          <label key={style} className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={selectedStyles.includes(style)}
              onChange={() => handleChange(style)}
              className="form-checkbox h-5 w-5 text-blue-600"
            />
            <span>{style}</span>
          </label>
        ))}
      </div>
    </div>
  );
};

export default StyleSelector;