import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import crypto from 'crypto';

// 生成安全的文件名
function generateSafeFileName(originalName: string): string {
  // 生成8位随机字符串
  const randomString = crypto.randomBytes(4).toString('hex');
  // 获取时间戳
  const timestamp = Date.now();
  // 提取原始文件名中的非特殊字符（可选）
  const safeOriginalName = originalName
    .replace(/[^a-zA-Z0-9]/g, '') // 只保留字母和数字
    .slice(0, 20); // 限制长度

  return `${timestamp}-${randomString}${safeOriginalName ? '-' + safeOriginalName : ''}.svg`;
}

// 检查文件是否为支持的格式
function isSupportedFormat(fileName: string): boolean {
  const lowerName = fileName.toLowerCase();
  return (
    lowerName.endsWith('.jpg') || 
    lowerName.endsWith('.jpeg')
  );
}

// 初始化 S3 客户端 (Cloudflare R2)
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  },
});

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const quality = parseInt(formData.get('quality') as string) || 85;
    const removeExif = formData.get('removeExif') === 'true';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // 检查文件类型
    if (!file.type && !isSupportedFormat(file.name)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPG and JPEG files are supported.' },
        { status: 400 }
      );
    }

    // 读取文件内容
    let buffer;
    try {
      buffer = Buffer.from(await file.arrayBuffer());
    } catch (error) {
      console.error('Error reading file:', error);
      return NextResponse.json(
        { error: 'Failed to read file' },
        { status: 500 }
      );
    }
    
    try {
      // Get image dimensions and optimize the image
      const processedImageBuffer = await sharp(buffer)
        .jpeg({ quality }) // Optimize the JPEG with the quality setting
        .toBuffer();
        
      const metadata = await sharp(processedImageBuffer).metadata();
      const width = metadata.width || 800;
      const height = metadata.height || 600;
      
      // Convert image to base64 for embedding in SVG
      const base64Image = processedImageBuffer.toString('base64');
      
      // Create SVG with embedded image
      const svgContent = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" 
     xmlns="http://www.w3.org/2000/svg" 
     xmlns:xlink="http://www.w3.org/1999/xlink">
  <title>JPG converted to SVG</title>
  <desc>Image converted from JPG to SVG using heic-tojpg.com</desc>
  <image width="${width}" height="${height}" xlink:href="data:image/jpeg;base64,${base64Image}" />
</svg>`;
      
      // 生成安全的文件名
      const fileName = generateSafeFileName(file.name);

      // 上传到Cloudflare R2
      try {
        await s3Client.send(
          new PutObjectCommand({
            Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
            Key: fileName,
            Body: Buffer.from(svgContent),
            ContentType: 'image/svg+xml',
            // 添加原始文件名作为元数据
            Metadata: {
              originalName: encodeURIComponent(file.name),
              exifRemoved: removeExif.toString()
            }
          })
        );
      } catch (error) {
        console.error('Error uploading to R2:', error);
        return NextResponse.json(
          { error: 'Failed to upload converted file' },
          { status: 500 }
        );
      }

      // 返回下载API的URL
      const downloadUrl = `/api/download?key=${encodeURIComponent(fileName)}`;

      return NextResponse.json({
        success: true,
        url: downloadUrl,
        originalName: file.name
      });
      
    } catch (error) {
      console.error('Error converting JPG to SVG:', error);
      return NextResponse.json(
        { error: 'Failed to convert JPG to SVG' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 