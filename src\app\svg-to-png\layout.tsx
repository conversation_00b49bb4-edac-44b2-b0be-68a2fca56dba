import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for SVG to PNG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free SVG to PNG Converter Online',
  description: 'Convert SVG to PNG online for free. Fast batch conversion with no watermarks. Transform vector graphics to raster PNG with perfect quality.',
  keywords: 'svg to png, convert svg to png, svg to png converter, convert svg file to png, .svg to png',
  alternates: {
    canonical: `${baseUrl}/svg-to-png`,
  },
  openGraph: {
    title: 'The Best Free SVG to PNG Converter Online',
    description: 'Convert SVG to PNG online for free. Transform vector graphics to raster format with perfect quality and transparency. No software installation required!',
    url: `${baseUrl}/svg-to-png`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/svg-to-png-converter-tool.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free SVG to PNG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free SVG to PNG Converter',
    description: 'Convert SVG to PNG online for free. Transform vector graphics to raster format with perfect quality and transparency. No software installation required!',
    images: [`https://image.heic-tojpg.com/svg-to-png-converter-tool.webp`],
  },
};

export default function SvgToPngLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 