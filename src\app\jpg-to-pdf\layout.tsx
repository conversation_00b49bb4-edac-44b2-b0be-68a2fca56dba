import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for JPG to PDF page, optimized for SEO
export const metadata: Metadata = {
  title: 'Best Free JPG to PDF Converter',
  description: 'Convert JPG to PDF online for free. Professional document quality with fast batch conversion. No watermarks, no file size limits. Format JPG to PDF in seconds!',
  keywords: 'jpg to pdf, convert jpg to pdf, jpg to pdf converter, format jpg to pdf, jpg format to pdf converter, jpg to pdf free',
  alternates: {
    canonical: `${baseUrl}/jpg-to-pdf`,
  },
  openGraph: {
    title: 'Best Free JPG to PDF Converter',
    description: 'Convert JPG to PDF online for free. Professional document quality with fast batch conversion. No watermarks, no file size limits. Format JPG to PDF in seconds!',
    url: `${baseUrl}/jpg-to-pdf`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/jpg-to-pdf-converter.webp`,
        width: 1200,
        height: 630,
        alt: 'Professional JPG to PDF Converter Tool',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Best Free JPG to PDF Converter',
    description: 'Convert JPG to PDF online for free. Professional document quality with fast batch conversion. No watermarks, no file size limits. Format JPG to PDF in seconds!',
    images: [`https://image.heic-tojpg.com/jpg-to-pdf-converter.webp`],
  },
};

export default function JpgToPdfLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 