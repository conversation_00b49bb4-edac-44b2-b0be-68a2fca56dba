'use client';

import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { FiUpload, FiImage, FiDownload, FiArrowUp, FiX, FiCheck } from 'react-icons/fi';
import { Breadcrumb } from '@/components/Breadcrumb';
import Link from 'next/link';
import Head from 'next/head';
import Script from 'next/script';
import RelatedTools from '@/components/RelatedTools';

interface FileItem {
  id: string;
  name: string;
  size: number;
  status: 'waiting' | 'converting' | 'done' | 'error';
  downloadUrl?: string;
  errorMessage?: string;
  file: File;
}

export default function SvgToJpg() {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [quality, setQuality] = useState(85);
  const [isConverting, setIsConverting] = useState(false);
  const [removeExif, setRemoveExif] = useState(false);
  const [consentPrivacy, setConsentPrivacy] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [convertProgress, setConvertProgress] = useState({ current: 0, total: 0 });
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Add canonical link
  useEffect(() => {
    let link = document.querySelector('link[rel="canonical"]');
    
    if (!link) {
      link = document.createElement('link');
      link.setAttribute('rel', 'canonical');
      link.setAttribute('href', 'https://heic-tojpg.com/svg-to-jpg');
      document.head.appendChild(link);
    } else {
      link.setAttribute('href', 'https://heic-tojpg.com/svg-to-jpg');
    }
  }, []);

  // Detect if mobile device after component mount
  useEffect(() => {
    setIsMobile(/mobile|android|ios/i.test(window.navigator.userAgent));
  }, []);

  // Check scroll position to show/hide back to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setError(null);
    
    // Validate file types
    const invalidFiles = acceptedFiles.filter(file => {
      const lowerName = file.name.toLowerCase();
      return !lowerName.endsWith('.svg');
    });
    
    if (invalidFiles.length > 0) {
      setError('Please only upload SVG files.');
      return;
    }

    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(7),
      name: file.name,
      size: file.size,
      status: 'waiting' as const,
      file: file
    }));
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/svg+xml': ['.svg', '.SVG']
    },
    maxFiles: 100,
    noDrag: isMobile,
    onDropRejected: () => {
      setError('Please only upload SVG files.');
    },
    onError: (error) => {
      setError('An error occurred while uploading files.');
      console.error('Dropzone error:', error);
    }
  });

  const handleConvert = async () => {
    if (!consentPrivacy) {
      setError('Please consent to the privacy policy before converting files.');
      return;
    }

    if (files.length === 0) {
      setError('Please add some files to convert.');
      return;
    }

    setError(null);
    setIsConverting(true);
    const filesToConvert = files.filter(f => f.status === 'waiting');
    setConvertProgress({ current: 0, total: filesToConvert.length });
    
    try {
      await Promise.all(filesToConvert.map(async (fileItem, index) => {
        setFiles(prev => 
          prev.map(f => 
            f.id === fileItem.id 
              ? { ...f, status: 'converting' } 
              : f
          )
        );
        setConvertProgress(prev => ({ ...prev, current: index + 1 }));

        try {
          const formData = new FormData();
          formData.append('file', fileItem.file);
          formData.append('quality', quality.toString());
          formData.append('removeExif', removeExif.toString());

          const response = await fetch('/api/convert-svg', {
            method: 'POST',
            body: formData,
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Conversion failed');
          }

          setFiles(prev => 
            prev.map(f => 
              f.id === fileItem.id 
                ? { ...f, status: 'done', downloadUrl: result.url } 
                : f
            )
          );
        } catch (error: any) {
          console.error('File conversion error:', error);
          const errorMessage = error.message || 'Failed to convert file';
          setFiles(prev => 
            prev.map(f => 
              f.id === fileItem.id 
                ? { ...f, status: 'error', errorMessage: errorMessage } 
                : f
            )
          );
          setError(`Error converting ${fileItem.name}: ${errorMessage}`);
        }
      }));
    } catch (error: any) {
      console.error('Batch conversion error:', error);
      setError('An error occurred during batch conversion. Please try again.');
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownloadAll = async () => {
    const completedFiles = files.filter(f => f.status === 'done' && f.downloadUrl);
    if (completedFiles.length === 0) {
      setError('No converted files to download');
      return;
    }

    setError(null);
    try {
      // Create a hidden download link to trigger download
      for (const file of completedFiles) {
        const link = document.createElement('a');
        link.href = file.downloadUrl!;
        link.download = file.name.replace('.svg', '').replace('.SVG', '') + '.jpg';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        // Add small delay to prevent browser from blocking multiple downloads
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error('Download error:', error);
      setError('Failed to download some files. Please try again.');
    }
  };

  const handleClearAll = () => {
    setFiles([]);
    setError(null);
  };

  const handleDeleteFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
    setError(null);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Scroll to top functionality
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <>
      <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative bg-gradient-to-b from-slate-50/80 via-white to-white">
        <Breadcrumb />
        <h1 className="text-2xl md:text-4xl font-bold text-center mb-4 md:mb-8 text-gray-800">
          The Best Free SVG to JPG Converter
        </h1>
        <p className="text-center text-gray-600 mb-6">
          Convert SVG images to standard JPG format online
        </p>

          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600">{error}</p>
            </div>
          )}

          <div className="mb-6 md:mb-8 relative">
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-6 md:p-8 text-center cursor-pointer transition-all duration-300 relative backdrop-blur-sm
                ${isDragActive 
                  ? 'border-indigo-500 bg-gradient-to-br from-indigo-50/40 to-white/80' 
                  : 'border-gray-300 hover:border-indigo-400 hover:bg-gradient-to-br hover:from-slate-50/50 hover:to-white/90'
                } shadow-sm hover:shadow-md`}
            >
              <input {...getInputProps()} accept=".svg,.SVG" />
              <FiImage className="mx-auto text-4xl md:text-5xl mb-3 text-indigo-400" />
              <p className="text-lg md:text-xl mb-2 text-gray-800">Select SVG Images</p>
              <p className="text-sm text-gray-600 mb-2">from your device</p>
              <p className="text-xs text-gray-500">Supports up to 100 files</p>
              
              {isMobile && (
                <div className="mt-4 text-sm text-indigo-600 font-medium">
                  Tap here to select images from your device
                </div>
              )}
            </div>
            {/* Buy me a coffee button outside the dropzone in the left bottom corner */}
            <a
              href="https://ko-fi.com/yourfriendlycreator"
              target="_blank"
              rel="noopener noreferrer"
              className="absolute -bottom-10 left-0 px-2 py-1 md:px-3 md:py-2 text-xs md:text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-all duration-200 flex items-center justify-center"
            >
              <span>☕Buy me a coffee</span>
            </a>
            <div className="absolute -bottom-10 left-[140px] md:left-[160px] text-xs md:text-sm text-gray-600 italic">
              I'll use the money to upgrade to a better server to help you with daily work.
            </div>
          </div>
          

          
          {files.length > 0 && (
            <div className="space-y-4 md:space-y-6">
              <div className="bg-white rounded-lg shadow-md overflow-x-auto border border-gray-100">
                <table className="min-w-full table-fixed">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                      <th className="w-[30%] sm:w-[40%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
                      <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                      <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {files.map((file, index) => (
                      <tr key={file.id}>
                        <td className="w-[10%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                        <td className="w-[30%] sm:w-[40%] px-4 py-3 text-sm font-medium text-gray-900">
                          <div className="truncate" title={file.name}>
                            {isMobile ? 
                              file.name.length > 10 ? 
                                file.name.slice(0, 7) + '...' + file.name.slice(-3) 
                                : file.name
                              : file.name
                            }
                          </div>
                        </td>
                        <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap">
                          {file.status === 'done' && file.downloadUrl ? (
                            <a 
                              href={file.downloadUrl} 
                              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                              download
                            >
                              Download
                            </a>
                          ) : file.status === 'error' ? (
                            <span className="text-red-500 text-sm" title={file.errorMessage}>Error</span>
                          ) : file.status === 'converting' ? (
                            <span className="text-yellow-500 text-sm">Converting...</span>
                          ) : (
                            <span className="text-gray-500 text-sm">Waiting</span>
                          )}
                        </td>
                        <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatFileSize(file.size)}</td>
                        <td className="w-[10%] px-4 py-3 whitespace-nowrap text-center">
                          {file.status === 'done' ? (
                            <FiCheck className="text-green-500 w-5 h-5 mx-auto" title="Conversion Complete" />
                          ) : (
                            <button 
                              onClick={() => handleDeleteFile(file.id)}
                              className="text-gray-500 hover:text-red-600 transition-colors duration-200 focus:outline-none"
                              disabled={file.status === 'converting'}
                              title="Delete File"
                            >
                              <FiX className="w-5 h-5" />
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="bg-gradient-to-br from-slate-50/90 via-white to-white p-4 md:p-6 rounded-lg shadow-md border border-gray-100">
                <h2 className="text-lg md:text-xl font-semibold mb-4 text-gray-800">Output Settings</h2>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Format:</label>
                    <select
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      value="JPG"
                      disabled
                    >
                      <option>JPG</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Quality:</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="range"
                        min="1"
                        max="100"
                        value={quality}
                        onChange={(e) => setQuality(parseInt(e.target.value))}
                        className="flex-1"
                      />
                      <span className="text-sm text-gray-600 w-12">{quality}%</span>
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={removeExif}
                      onChange={(e) => setRemoveExif(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Remove all EXIF information</span>
                  </label>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    checked={consentPrivacy}
                    onChange={(e) => setConsentPrivacy(e.target.checked)}
                    className="mt-1 w-5 h-5 md:w-4 md:h-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    required
                  />
                  <label className="text-sm text-gray-600">
                    I consent to heic-tojpg.com collecting and processing my data according to{' '}
                    <Link href="/privacy-policy" className="text-indigo-600 hover:text-indigo-800 underline">
                      Privacy Policy
                    </Link>
                    .
                  </label>
                </div>

                <div className="flex flex-col sm:flex-row gap-2 sm:justify-end sm:space-x-3">
                  <button
                    onClick={handleClearAll}
                    className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                    disabled={isConverting}
                  >
                    Clear all
                  </button>
                  <button
                    onClick={handleConvert}
                    disabled={isConverting || !files.some(f => f.status === 'waiting') || !consentPrivacy}
                    className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    {isConverting ? 'Converting...' : 'Convert'}
                  </button>
                  <button
                    onClick={handleDownloadAll}
                    disabled={!files.some(f => f.status === 'done')}
                    className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 transition-colors duration-200"
                  >
                    <FiDownload className="w-4 h-4" />
                    <span>Download all</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Share buttons */}
          <div className="mt-12 p-4 bg-gradient-to-br from-slate-50/90 via-white to-white rounded-lg shadow-md border border-gray-100">
            <h3 className="text-lg font-semibold mb-4 text-gray-800">Share with Friends</h3>
            <p className="text-sm text-gray-600 mb-4">If you found this tool useful, please share it with friends who might need it!</p>
            <div className="sharethis-inline-share-buttons"></div>
          </div>
          {/* Related tools */}
          <RelatedTools currentTool="SVG to JPG" />

          {/* Conversion progress indicator */}
          {isConverting && isMobile && (
            <div className="fixed bottom-4 left-0 right-0 mx-4 bg-gradient-to-r from-indigo-50/90 via-white to-blue-50/90 p-4 rounded-lg shadow-lg border border-indigo-100 backdrop-blur-sm">
              <div className="text-center text-sm text-indigo-800 font-medium">
                Converting: {convertProgress.current} of {convertProgress.total} files
              </div>
              <div className="mt-2 h-2 bg-indigo-100 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-indigo-600 to-indigo-500 transition-all duration-300" 
                  style={{ width: `${(convertProgress.current / convertProgress.total) * 100}%` }}
                />
              </div>
            </div>
          )}

          <div className="mt-12 space-y-16">
            <section className="introduction">
              <h2 className="text-2xl font-bold mb-6 text-gray-800">
                <a href="/svg-to-jpg" className="hover:text-indigo-600 transition-colors">SVG to JPG</a> Converter Features
              </h2>
              <div className="space-y-16">
                {/* Feature Group 1: Free & Easy + Fast Conversion */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">1</span>
                        Professional SVG to JPG Conversion - 100% Free
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>No registration or payment required - completely free tool to convert SVG to JPG with enterprise-grade vector rasterization quality</li>
                        <li>Intuitive drag-and-drop interface with one-click upload functionality for efficient batch processing of multiple .SVG files</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">2</span>
                        High-Speed SVG to JPG Conversion with Vector-to-Raster Precision
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Using advanced vector graphic rendering algorithms for converting SVG to JPG with pixel-perfect accuracy</li>
                        <li>Maintains crisp edges and smooth gradients with anti-aliasing technology when converting .SVG to .JPG format</li>
                      </ul>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/svg-to-jpg-converter-tool.webp" 
                      alt="Professional SVG to JPG Converter Tool" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* Feature Group 2: Security & Privacy */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://cdn.pixabay.com/photo/2018/01/17/20/22/analytics-3088958_1280.jpg" 
                      alt="Online Convert SVG to JPG Format Converter" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">3</span>
                        Watermark-Free & Unlimited SVG Converter to JPG
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Converted JPG files are completely watermark-free, ready for professional design projects, websites, or printing after converting SVG to JPG</li>
                        <li>No file size limits, no quantity restrictions - change SVG to JPG anytime, anywhere with our robust SVG converter to JPG</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">4</span>
                        SVG to JPG – End-to-End Encryption & Privacy Protection
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Employing AES-256 bit encryption standards to ensure SVG file security during transmission and processing</li>
                        <li>Using convert-and-delete technology - files are immediately removed from servers after converting SVG to JPG</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Feature Group 3: Batch Processing & Compatibility */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">5</span>
                        Efficient Batch SVG to JPG Conversion Technology
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Multi-threaded processing technology to simultaneously convert multiple .SVG to .JPG format files</li>
                        <li>Perfect for UX designers and developers who need to convert vector graphics to raster JPG for cross-platform compatibility</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">6</span>
                        Cross-Platform SVG to JPG Conversion Compatibility
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Our SVG converter to JPG supports all major browsers including Chrome, Firefox, Edge, Safari and more</li>
                        <li>Compatible with Windows, macOS, Linux, Android, and iOS devices - a truly universal tool for converting SVG to JPG</li>
                      </ul>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://cdn.pixabay.com/photo/2016/03/27/18/54/technology-1283624_1280.jpg" 
                      alt="Batch SVG to JPG Conversion Compatibility" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* Feature Group 4: Quality Control & Online Access */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://cdn.pixabay.com/photo/2016/11/29/06/18/home-office-1867761_1280.jpg" 
                      alt="Online SVG to JPG Quality Control" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">7</span>
                        Professional SVG to JPG Converter with Advanced Image Processing
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Optimized JPG compression parameters, balancing image quality and file size when converting SVG to JPG</li>
                        <li>Supports color profile management and gamma correction for precise color reproduction during SVG to JPG transformation</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                        <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">8</span>
                        Cloud-Based SVG to JPG Conversion - No Software Installation Required
                      </h3>
                      <ul className="space-y-2 text-gray-600 list-disc pl-11">
                        <li>Pure cloud processing - no software or plugins needed to convert .SVG to .JPG files</li>
                        <li>WebAssembly optimization technology for efficient browser-based processing on any device</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <section className="what-is">
              <h2 className="text-2xl font-bold mb-6 text-gray-800">Understanding the SVG to JPG Converter</h2>
              
              <div className="space-y-16">
                {/* SVG Introduction Group */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is SVG to JPG Conversion?</h3>
                      <p className="text-gray-600">
                        SVG to JPG conversion is the process of transforming Scalable Vector Graphics (SVG) into the universally compatible JPEG format. While SVG offers resolution-independent vector graphics,
                        many platforms require raster formats like JPG. Our SVG to JPG converter ensures optimal vector-to-raster transformation with accurate rendering of paths, gradients, and transparency.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is the SVG Format?</h3>
                      <p className="text-gray-600">
                        SVG (Scalable Vector Graphics) is an XML-based vector image format that defines graphics using mathematical expressions rather than pixel grids. This allows SVG files to scale infinitely without quality loss,
                        making them ideal for logos, icons, and illustrations. Despite these advantages, converting SVG to JPG is often necessary for wider compatibility with applications that don't support vector formats. You can visit: <a href="https://en.wikipedia.org/wiki/Scalable_Vector_Graphics" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">the Wikipedia page on SVG</a>.
                      </p>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/the-wikipedia-page-on-svg.webp" 
                      alt="Professional Analysis of SVG Format" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* JPG Details Group */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://image.heic-tojpg.com/the-wikipedia-page-on-jpg.webp" 
                      alt="Detailed Explanation of JPG Files" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is a JPG File?</h3>
                      <p className="text-gray-600">
                        JPG (or JPEG, Joint Photographic Experts Group) is a raster image format that uses lossy compression to create smaller file sizes. It supports up to 16.7 million colors and is widely used for photographs and complex graphics.
                        JPG files are universally supported across all platforms and applications, making converting SVG to JPG essential when vector graphics need to be shared in a more compatible format. You can visit: <a href="https://en.wikipedia.org/wiki/JPEG" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">the Wikipedia page on JPEG</a>.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">What is Vector-to-Raster Conversion?</h3>
                      <p className="text-gray-600">
                        Vector-to-raster conversion is the technical process that happens when converting SVG to JPG. This transformation changes mathematically-defined graphics into pixel-based images through a process called rasterization. 
                        When using our SVG to JPG converter, this process is optimized with anti-aliasing, proper DPI settings, and quality preservation techniques to ensure the best possible output when converting .SVG to .JPG format.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Format Comparison Group */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">SVG vs. JPG: Understanding the Differences</h3>
                      <p className="text-gray-600">
                        While SVG offers scalability and editability through its vector-based structure, JPG provides universal compatibility with its raster-based format. SVG files maintain perfect quality at any size but require applications that support vector rendering.
                        Converting SVG to JPG creates a fixed-resolution image that can be viewed anywhere but cannot be scaled infinitely. Our SVG converter to JPG bridges this gap by producing high-quality JPG files from your vector graphics.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">Which Image Formats Can I Upload?</h3>
                      <p className="text-gray-600">
                        Our SVG to JPG converter primarily supports SVG files (.svg extension). This specialized tool is designed to efficiently convert vector graphics to raster JPG format while preserving visual fidelity through advanced rendering techniques.
                        The conversion process utilizes optimal anti-aliasing and subpixel rendering to ensure the highest quality output when converting SVG to JPG.
                      </p>
                    </div>
                  </div>
                  <div className="relative">
                    <img 
                      src="https://image.heic-tojpg.com/svg-vs-jpg-format-comparison.webp" 
                      alt="SVG vs JPG Format Comparison" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                </div>

                {/* Conversion Benefits Group */}
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative order-2 md:order-1">
                    <img 
                      src="https://cdn.pixabay.com/photo/2015/01/08/18/27/startup-593341_1280.jpg" 
                      alt="Benefits of SVG to JPG Conversion" 
                      className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                    />
                  </div>
                  <div className="space-y-8 order-1 md:order-2">
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">Why Convert SVG to JPG?</h3>
                      <p className="text-gray-600">
                        Converting SVG to JPG ensures maximum compatibility across all applications, platforms, and devices. While SVG offers excellent scalability, many content management systems, email clients, and older software don't fully support vector graphics.
                        Using our SVG to JPG converter provides universal compatibility, eliminating potential issues when sharing your vector-based designs in environments that require raster formats.
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-gray-800">Technical Benefits of JPG Format After Converting SVG</h3>
                      <p className="text-gray-600">
                        After converting SVG to JPG, files gain several technical advantages including wider software compatibility, smaller file sizes, and faster loading times for web use. When you convert .SVG to .JPG,
                        you also ensure your graphics can be viewed across all platforms without specialized vector rendering capabilities, making JPG an excellent universal format for both digital and print applications.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <section className="how-to">
              <h2 className="text-2xl font-bold mb-6 text-gray-800">How to Use the SVG to JPG Converter</h2>
              
              <div className="grid md:grid-cols-2 gap-8 items-center mb-12">
                <div>
                  <ol className="space-y-6 relative">
                    <div className="absolute top-0 bottom-0 left-4 w-0.5 bg-indigo-100"></div>
                    <li className="relative pl-12">
                      <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">1</div>
                      <h3 className="text-lg font-semibold mb-2 text-gray-800">Upload SVG Files</h3>
                      <p className="text-gray-600">
                        Drag and drop your SVG files into the conversion area, or click to select files from your device. Our SVG to JPG converter supports batch uploading of multiple files for simultaneous processing, increasing efficiency for designers and developers.
                      </p>
                    </li>
                    <li className="relative pl-12">
                      <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">2</div>
                      <h3 className="text-lg font-semibold mb-2 text-gray-800">Choose Conversion Settings</h3>
                      <p className="text-gray-600">
                        Adjust JPG quality settings to optimize your output when converting SVG to JPG. You can customize the compression level and choose to preserve or remove metadata for enhanced control over the final result when converting from vector to raster format.
                      </p>
                    </li>
                    <li className="relative pl-12">
                      <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">3</div>
                      <h3 className="text-lg font-semibold mb-2 text-gray-800">Convert and Download</h3>
                      <p className="text-gray-600">
                        Click the "Convert" button to start the SVG to JPG conversion process. Our SVG converter to JPG will process your files with advanced vector rendering algorithms. Once completed, you can download your converted files individually or use our batch download option.
                      </p>
                    </li>
                  </ol>
                </div>
                <div className="relative">
                  <img 
                    src="https://cdn.pixabay.com/photo/2015/05/31/10/55/man-791049_1280.jpg" 
                    alt="SVG to JPG Conversion Process" 
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>
            </section>
          </div>

          
          <section className="why-use mb-8 mt-12">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">Why Choose Our SVG to JPG Converter</h2>
            
            <div className="space-y-16">
              {/* Reason 1 */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800">Universal Compatibility</h3>
                  <p className="text-gray-600">
                    While SVG offers excellent scalability through XML-based vector paths, many applications and devices don't fully support this format. Our SVG to JPG converter ensures your graphics can be viewed, edited, and shared across all platforms without compatibility issues.
                    The conversion process preserves visual fidelity through advanced anti-aliasing and pixel-perfect rendering techniques.
                  </p>
                  <p className="text-gray-600">
                    Converting SVG to JPG maintains optimal image quality while changing from vector to raster format, using sophisticated interpolation and dithering techniques to provide the highest fidelity conversion possible for your vector graphics.
                  </p>
                </div>
                <div className="relative">
                  <img 
                    src="https://cdn.pixabay.com/photo/2016/11/23/14/37/blur-1853262_1280.jpg" 
                    alt="SVG to JPG Universal Compatibility" 
                    className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                  />
                </div>
              </div>
              
              {/* Reason 2 */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img 
                    src="https://cdn.pixabay.com/photo/2015/07/17/22/43/student-849825_1280.jpg" 
                    alt="Simplified SVG to JPG Workflow" 
                    className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                  />
                </div>
                <div className="space-y-4 order-1 md:order-2">
                  <h3 className="text-lg font-semibold text-gray-800">Simplified Design Workflow</h3>
                  <p className="text-gray-600">
                    When working with image files across multiple applications, converting SVG to JPG can streamline your workflow. This transformation helps eliminate compatibility issues when importing vector graphics into different design software or content management systems,
                    especially platforms that don't fully support the SVG MIME type or vector rendering.
                  </p>
                  <p className="text-gray-600">
                    Our SVG converter to JPG's batch processing feature allows you to convert multiple .SVG to .JPG files simultaneously, supporting parallel multi-task processing that saves valuable time and effort in your design or development projects.
                  </p>
                </div>
              </div>
              
              {/* Reason 3 */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800">Privacy and Security</h3>
                  <p className="text-gray-600">
                    Using our SVG to JPG converter tool, you can choose to remove metadata from your images during the conversion process. This feature is particularly important for privacy-conscious users, 
                    allowing you to share images without revealing sensitive information when you convert vector graphics to JPG format.
                  </p>
                  <p className="text-gray-600">
                    Our secure conversion infrastructure employs TLS encryption and secure file handling protocols, ensuring your SVG files remain private throughout the conversion process. All uploaded files are automatically deleted after converting SVG to JPG,
                    providing peace of mind for security-conscious users and organizations.
                  </p>
                </div>
                <div className="relative">
                  <img 
                    src="https://cdn.pixabay.com/photo/2017/10/10/21/46/laptop-2838917_1280.jpg" 
                    alt="SVG to JPG Privacy Protection" 
                    className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                  />
                </div>
              </div>
              
              {/* Reason 4 */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img 
                    src="https://image.heic-tojpg.com/jfif-to-jpg-web-optimization.webp" 
                    alt="SVG to JPG Quality Preservation" 
                    className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                  />
                </div>
                <div className="space-y-4 order-1 md:order-2">
                  <h3 className="text-lg font-semibold text-gray-800">Professional Image Quality</h3>
                  <p className="text-gray-600">
                    Our SVG to JPG converter uses advanced image processing algorithms to ensure the highest quality output. The conversion process implements optimal DPI settings and anti-aliasing techniques to preserve visual fidelity,
                    making it ideal for professional graphic designers, web developers, and digital artists who need to convert vector graphics to raster format without quality loss.
                  </p>
                  <p className="text-gray-600">
                    When converting SVG to JPG, our tool applies sophisticated rendering techniques that ensure smooth gradients, clean edges, and accurate color reproduction. This attention to detail makes our converter perfect for transforming logos, illustrations, infographics, and technical diagrams.
                  </p>
                </div>
              </div>
            </div>
          </section>

          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-bold mb-4">Frequently Asked Questions About SVG to JPG Conversion</h2>
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-gray-900">What's the difference between SVG and JPG?</h3>
                <p className="mt-1 text-gray-700">
                  SVG is a vector format that uses mathematical formulas to define graphics, allowing infinite scaling without quality loss. JPG is a raster format that stores images as pixel grids with lossy compression.
                  While SVG files are resolution-independent and typically best for logos and illustrations, converting SVG to JPG creates fixed-resolution images that are universally compatible across all platforms and applications.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Will I lose quality when converting SVG to JPG?</h3>
                <p className="mt-1 text-gray-700">
                  When converting from SVG to JPG using our converter, there is some quality transformation as vector graphics become rasterized. However, our SVG to JPG converter minimizes quality loss through advanced rendering algorithms, high DPI settings, and optimal anti-aliasing.
                  You can also adjust the quality setting to control the balance between image fidelity and file size when converting .SVG to .JPG format.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Is it safe to convert SVG to JPG online?</h3>
                <p className="mt-1 text-gray-700">
                  Yes, our online SVG converter to JPG follows strict security protocols when handling all files. Your vector graphics are briefly processed on our secure servers and then automatically deleted.
                  We don't permanently store your uploaded files or use them for any other purpose. All conversion processes take place in a TLS-encrypted secure environment, ensuring your SVG to JPG conversion
                  is completely safe and reliable.
                </p>
              </div>
            </div>
          </div>
      </main>

      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 p-3 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-full shadow-lg hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 z-50"
          aria-label="Scroll to top"
        >
          <FiArrowUp className="w-6 h-6" />
        </button>
      )}
    </>
  );
} 