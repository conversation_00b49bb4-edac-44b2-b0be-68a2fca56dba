'use client';

import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { FiUpload, FiImage, FiDownload, FiArrowUp, FiX, FiCheck } from 'react-icons/fi';
import { Breadcrumb } from '@/components/Breadcrumb';
import Link from 'next/link';
import Head from 'next/head';
import Script from 'next/script';
import RelatedTools from '@/components/RelatedTools';

interface FileItem {
  id: string;
  name: string;
  size: number;
  status: 'waiting' | 'converting' | 'done' | 'error';
  downloadUrl?: string;
  errorMessage?: string;
  file: File;
}

export default function WebpToAvif() {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [quality, setQuality] = useState(85);
  const [isConverting, setIsConverting] = useState(false);
  const [removeExif, setRemoveExif] = useState(false);
  const [consentPrivacy, setConsentPrivacy] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [convertProgress, setConvertProgress] = useState({ current: 0, total: 0 });
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Add canonical link
  useEffect(() => {
    let link = document.querySelector('link[rel="canonical"]');

    if (!link) {
      link = document.createElement('link');
      link.setAttribute('rel', 'canonical');
      link.setAttribute('href', 'https://heic-tojpg.com/webp-to-avif');
      document.head.appendChild(link);
    } else {
      link.setAttribute('href', 'https://heic-tojpg.com/webp-to-avif');
    }
  }, []);

  // Detect if mobile device after component mount
  useEffect(() => {
    setIsMobile(/mobile|android|ios/i.test(window.navigator.userAgent));
  }, []);

  // Check scroll position to show/hide back to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setError(null);

    // Validate file types
    const invalidFiles = acceptedFiles.filter(file => {
      const lowerName = file.name.toLowerCase();
      return !lowerName.endsWith('.webp');
    });

    if (invalidFiles.length > 0) {
      setError('Please only upload WebP files.');
      return;
    }

    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(7),
      name: file.name,
      size: file.size,
      status: 'waiting' as const,
      file: file
    }));
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/webp': ['.webp', '.WEBP']
    },
    maxFiles: 100,
    noDrag: isMobile,
    onDropRejected: () => {
      setError('Please only upload WebP files.');
    },
    onError: (error) => {
      setError('An error occurred while uploading files.');
      console.error('Dropzone error:', error);
    }
  });

  const handleConvert = async () => {
    if (!consentPrivacy) {
      setError('Please consent to the privacy policy before converting files.');
      return;
    }

    if (files.length === 0) {
      setError('Please add some files to convert.');
      return;
    }

    setError(null);
    setIsConverting(true);
    const filesToConvert = files.filter(f => f.status === 'waiting');
    setConvertProgress({ current: 0, total: filesToConvert.length });

    try {
      await Promise.all(filesToConvert.map(async (fileItem, index) => {
        setFiles(prev =>
          prev.map(f =>
            f.id === fileItem.id
              ? { ...f, status: 'converting' }
              : f
          )
        );
        setConvertProgress(prev => ({ ...prev, current: index + 1 }));

        try {
          const formData = new FormData();
          formData.append('file', fileItem.file);
          formData.append('quality', quality.toString());
          formData.append('removeExif', removeExif.toString());

          const response = await fetch('/api/webp-to-avif', {
            method: 'POST',
            body: formData,
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Conversion failed');
          }

          setFiles(prev =>
            prev.map(f =>
              f.id === fileItem.id
                ? { ...f, status: 'done', downloadUrl: result.url }
                : f
            )
          );
        } catch (error: any) {
          console.error('File conversion error:', error);
          const errorMessage = error.message || 'Failed to convert file';
          setFiles(prev =>
            prev.map(f =>
              f.id === fileItem.id
                ? { ...f, status: 'error', errorMessage: errorMessage }
                : f
            )
          );
          setError(`Error converting ${fileItem.name}: ${errorMessage}`);
        }
      }));
    } catch (error: any) {
      console.error('Batch conversion error:', error);
      setError('An error occurred during batch conversion. Please try again.');
    } finally {
      setIsConverting(false);
    }
  };

  const handleDownloadAll = async () => {
    const completedFiles = files.filter(f => f.status === 'done' && f.downloadUrl);
    if (completedFiles.length === 0) {
      setError('No converted files to download');
      return;
    }

    setError(null);
    try {
      // Create a hidden download link to trigger download
      for (const file of completedFiles) {
        const link = document.createElement('a');
        link.href = file.downloadUrl!;
        link.download = file.name.replace('.webp', '').replace('.WEBP', '') + '.avif';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        // Add small delay to prevent browser from blocking multiple downloads
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error('Download error:', error);
      setError('Failed to download some files. Please try again.');
    }
  };

  const handleClearAll = () => {
    setFiles([]);
    setError(null);
  };

  const handleDeleteFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
    setError(null);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Scroll to top functionality
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <>
      <main className="min-h-screen p-4 md:p-8 max-w-4xl mx-auto relative bg-gradient-to-b from-slate-50/80 via-white to-white">
        <Breadcrumb />
        <h1 className="text-2xl md:text-4xl font-bold text-center mb-4 md:mb-8 text-gray-800">
          The Best Free WebP to AVIF Converter
        </h1>
        <p className="text-center text-gray-600 mb-6">
          Convert WebP photos to modern AVIF format online
        </p>

        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        <div className="mb-6 md:mb-8 relative">
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-6 md:p-8 text-center cursor-pointer transition-all duration-300 relative backdrop-blur-sm
                ${isDragActive
                ? 'border-indigo-500 bg-gradient-to-br from-indigo-50/40 to-white/80'
                : 'border-gray-300 hover:border-indigo-400 hover:bg-gradient-to-br hover:from-slate-50/50 hover:to-white/90'
              } shadow-sm hover:shadow-md`}
          >
            <input {...getInputProps()} accept=".webp,.WEBP" />
            <FiImage className="mx-auto text-4xl md:text-5xl mb-3 text-indigo-400" />
            <p className="text-lg md:text-xl mb-2 text-gray-800">Select WebP Photos</p>
            <p className="text-sm text-gray-600 mb-2">from your device</p>
            <p className="text-xs text-gray-500">Supports up to 100 files</p>

            {isMobile && (
              <div className="mt-4 text-sm text-indigo-600 font-medium">
                Tap here to select photos from your device
              </div>
            )}
          </div>
            {/* Buy me a coffee button outside the dropzone in the left bottom corner */}
            <a
              href="https://ko-fi.com/yourfriendlycreator"
              target="_blank"
              rel="noopener noreferrer"
              className="absolute -bottom-10 left-0 px-2 py-1 md:px-3 md:py-2 text-xs md:text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-all duration-200 flex items-center justify-center"
            >
              <span>☕Buy me a coffee</span>
            </a>
            <div className="absolute -bottom-10 left-[140px] md:left-[160px] text-xs md:text-sm text-gray-600 italic">
              I'll use the money to upgrade to a better server to help you with daily work.
            </div>
        </div>

        {files.length > 0 && (
          <div className="space-y-4 md:space-y-6">
            <div className="bg-white rounded-lg shadow-md overflow-x-auto border border-gray-100">
              <table className="min-w-full table-fixed">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                    <th className="w-[30%] sm:w-[40%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
                    <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="w-[25%] sm:w-[20%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                    <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {files.map((file, index) => (
                    <tr key={file.id}>
                      <td className="w-[10%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{index + 1}</td>
                      <td className="w-[30%] sm:w-[40%] px-4 py-3 text-sm font-medium text-gray-900">
                        <div className="truncate" title={file.name}>
                          {isMobile ?
                            file.name.length > 10 ?
                              file.name.slice(0, 7) + '...' + file.name.slice(-3)
                              : file.name
                            : file.name
                          }
                        </div>
                      </td>
                      <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap">
                        {file.status === 'done' && file.downloadUrl ? (
                          <a
                            href={file.downloadUrl}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                            download
                          >
                            Download
                          </a>
                        ) : file.status === 'error' ? (
                          <span className="text-red-500 text-sm" title={file.errorMessage}>Error</span>
                        ) : file.status === 'converting' ? (
                          <span className="text-yellow-500 text-sm">Converting...</span>
                        ) : (
                          <span className="text-gray-500 text-sm">Waiting</span>
                        )}
                      </td>
                      <td className="w-[25%] sm:w-[20%] px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatFileSize(file.size)}</td>
                      <td className="w-[10%] px-4 py-3 whitespace-nowrap text-center">
                        {file.status === 'done' ? (
                          <FiCheck className="text-green-500 w-5 h-5 mx-auto" title="Conversion Complete" />
                        ) : (
                          <button
                            onClick={() => handleDeleteFile(file.id)}
                            className="text-gray-500 hover:text-red-600 transition-colors duration-200 focus:outline-none"
                            disabled={file.status === 'converting'}
                            title="Delete File"
                          >
                            <FiX className="w-5 h-5" />
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="bg-gradient-to-br from-slate-50/90 via-white to-white p-4 md:p-6 rounded-lg shadow-md border border-gray-100">
              <h2 className="text-lg md:text-xl font-semibold mb-4 text-gray-800">Output Settings</h2>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Format:</label>
                  <select
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    value="AVIF"
                    disabled
                  >
                    <option>AVIF</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Quality:</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="1"
                      max="100"
                      value={quality}
                      onChange={(e) => setQuality(parseInt(e.target.value))}
                      className="flex-1"
                    />
                    <span className="text-sm text-gray-600 w-12">{quality}%</span>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={removeExif}
                    onChange={(e) => setRemoveExif(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Remove all EXIF information</span>
                </label>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  checked={consentPrivacy}
                  onChange={(e) => setConsentPrivacy(e.target.checked)}
                  className="mt-1 w-5 h-5 md:w-4 md:h-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  required
                />
                <label className="text-sm text-gray-600">
                  I consent to heic-tojpg.com collecting and processing my data according to{' '}
                  <Link href="/privacy-policy" className="text-indigo-600 hover:text-indigo-800 underline">
                    Privacy Policy
                  </Link>
                  .
                </label>
              </div>

              <div className="flex flex-col sm:flex-row gap-2 sm:justify-end sm:space-x-3">
                <button
                  onClick={handleClearAll}
                  className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                  disabled={isConverting}
                >
                  Clear all
                </button>
                <button
                  onClick={handleConvert}
                  disabled={isConverting || !files.some(f => f.status === 'waiting') || !consentPrivacy}
                  className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-transparent rounded-md shadow-sm text-white bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  {isConverting ? 'Converting...' : 'Convert'}
                </button>

                <button
                  onClick={handleDownloadAll}
                  disabled={!files.some(f => f.status === 'done')}
                  className="w-full sm:w-auto px-3 py-2 md:px-4 md:py-2 text-sm border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 transition-colors duration-200"
                >
                  <FiDownload className="w-4 h-4" />
                  <span>Download all</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Share buttons */}
        <div className="mt-12 p-4 bg-gradient-to-br from-slate-50/90 via-white to-white rounded-lg shadow-md border border-gray-100">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">Share with Friends</h3>
          <p className="text-sm text-gray-600 mb-4">If you found this tool useful, please share it with friends who might need it!</p>
          <div className="sharethis-inline-share-buttons"></div>
        </div>
        {/* Related tools */}
        <RelatedTools currentTool="WebP to AVIF" />


        {/* Conversion progress indicator */}
        {isConverting && isMobile && (
          <div className="fixed bottom-4 left-0 right-0 mx-4 bg-gradient-to-r from-indigo-50/90 via-white to-blue-50/90 p-4 rounded-lg shadow-lg border border-indigo-100 backdrop-blur-sm">
            <div className="text-center text-sm text-indigo-800 font-medium">
              Converting: {convertProgress.current} of {convertProgress.total} files
            </div>
            <div className="mt-2 h-2 bg-indigo-100 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-indigo-600 to-indigo-500 transition-all duration-300"
                style={{ width: `${(convertProgress.current / convertProgress.total) * 100}%` }}
              />
            </div>
          </div>
        )}

        <div className="mt-12 space-y-16">
          <section className="introduction">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">
              <a href="/webp-to-avif" className="hover:text-indigo-600 transition-colors">WebP to AVIF</a> Converter Features
            </h2>
            <div className="space-y-16">
              {/* Feature Group 1: Free & Easy + Fast Conversion */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">1</span>
                      Enterprise-Grade WebP to AVIF Free Converter - Zero Cost Solution
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>No registration or payment required - our webp to avif free tool employs advanced AV1 codec technology with enterprise-level encoding parameters</li>
                      <li>Intuitive drag-and-drop interface with intelligent memory allocation for optimized webp to avif bulk processing without browser crashes</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">2</span>
                      High-Throughput WebP to AVIF Conversion with Lossless Quality Preservation
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Leveraging state-of-the-art AV1 codec algorithms with advanced entropy encoding for superior compression ratios when you convert webp to avif</li>
                      <li>Maintains full 10-bit HDR color profiles and ICC color management with proprietary perceptual quantization algorithms</li>
                    </ul>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://image.heic-tojpg.com/professional-webp-to-png-converter-tool.webp"
                    alt="Professional WebP to PNG Converter Tool"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* Feature Group 2: Security & Privacy */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://cdn.pixabay.com/photo/2018/01/17/20/22/analytics-3088958_1280.jpg"
                    alt="Online Convert WebP to PNG Format Converter"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">3</span>
                      Artifact-Free & Unlimited WebP to AVIF Converter Engine
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Optimized AV1 codec implementation producing pristine webp to avif conversion results suitable for production-grade web applications</li>
                      <li>No file size restrictions, no batch limitations - convert webp to avif in bulk with our enterprise-scale processing infrastructure</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">4</span>
                      WebP to AVIF – Military-Grade Encryption & Data Protection
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Implementing AES-256 bit encryption with ephemeral key exchange to safeguard WebP assets during processing lifecycle</li>
                      <li>Utilizing secure compute infrastructure with memory-safe encoder implementations for reliable webp to avif conversion</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Feature Group 3: Batch Processing & Compatibility */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">5</span>
                      Parallelized WebP to AVIF Bulk Processing Architecture
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Multi-threaded WebAssembly implementation for concurrent processing to efficiently convert webp to avif in bulk at scale</li>
                      <li>Optimized for web developers and content delivery networks requiring industrial-strength webp to avif converter performance</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">6</span>
                      Universal WebP to AVIF Conversion Compatibility
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Our webp to avif converter supports all modern browsers with dav1d decoder compatibility including Chrome, Firefox, Edge, and Safari</li>
                      <li>Compatible with Windows, macOS, Linux, Android, and iOS devices with progressive enhancement fallback strategies</li>
                    </ul>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://cdn.pixabay.com/photo/2016/03/27/18/54/technology-1283624_1280.jpg"
                    alt="Batch WebP to AVIF Conversion Compatibility"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* Feature Group 4: Quality Control & Online Access */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://cdn.pixabay.com/photo/2016/11/29/06/18/home-office-1867761_1280.jpg"
                    alt="Online WebP to PNG Quality Control"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">7</span>
                      Enterprise-Class WebP to AVIF Converter with Advanced Psychovisual Optimization
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Fine-tuned AVIF compression parameters utilizing AV1 codec's advanced intra-frame prediction, achieving 62% better compression than WebP</li>
                      <li>Preserves full chroma sampling and supports both lossy and lossless encoding modes when you convert webp to avif files</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800 flex items-center">
                      <span className="bg-indigo-100 w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0">8</span>
                      Serverless WebP to AVIF Conversion - Zero Installation Solution
                    </h3>
                    <ul className="space-y-2 text-gray-600 list-disc pl-11">
                      <li>Edge-computing architecture - no software dependencies needed to convert webp to avif files in your browser</li>
                      <li>SIMD-accelerated WebAssembly implementation with advanced memory management for efficient converter webp to avif operations</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section className="what-is">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">Understanding the WebP to AVIF Technology</h2>

            <div className="space-y-16">
              {/* WebP Introduction Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">What is WebP to AVIF Conversion?</h3>
                    <p className="text-gray-600">
                      WebP to AVIF conversion represents the technological evolution from Google's VP8-based WebP format to the next-generation AV1-based AVIF format. 
                      While WebP utilizes VP8/VP9 codec technology, AVIF leverages the advanced AV1 video codec, enabling revolutionary compression efficiency with up to 62% smaller file sizes while maintaining superior visual fidelity. Our webp to avif converter implements optimal codec parameters to ensure maximum compression without perceptual quality degradation.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Technical Specifications of AVIF Format</h3>
                    <p className="text-gray-600">
                      AVIF (AV1 Image File Format) is a royalty-free image format standardized by the Alliance for Open Media, built on the AV1 video codec. Its technical specifications include support for 10/12-bit HDR color depth, 4:4:4 and 4:2:0 chroma subsampling, alpha transparency, and CICP/ICC color profile management.
                      When you convert webp to avif, you gain access to these advanced features plus significantly smaller file sizes due to AVIF's superior entropy coding and partitioning schemes. Reference: <a href="https://aomediacodec.github.io/av1-avif/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">AV1 Image File Format Specification</a>.
                    </p>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://image.heic-tojpg.com/the-wikipedia-page-on-webp.webp"
                    alt="Professional Analysis of WebP Format"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* AVIF Details Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://image.heic-tojpg.com/the-wikipedia-page-on-png.webp"
                    alt="Detailed Explanation of AVIF Files"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">AVIF File Format Specification</h3>
                    <p className="text-gray-600">
                      AVIF files are container-based image formats that utilize the HEIF (ISO/IEC 23008-12) container structure with AV1 intra-frame coded content. This architecture enables advanced features including multi-layered image composition, animation sequences, and progressive decoding capabilities.
                      Our webp to avif free converter tool preserves all essential image attributes while implementing the optimal AV1 encoding parameters for superior compression efficiency. The technical superiority of AVIF makes it the ideal target format when you need to convert webp to avif for modern web applications.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">AV1 Codec Technical Deep Dive</h3>
                    <p className="text-gray-600">
                      AV1 is an open-source, royalty-free video codec developed by the Alliance for Open Media that introduces revolutionary compression technologies including asymmetric numeral systems entropy coding, compound prediction modes, and variable-size transforms. When using our webp to avif converter,
                      these advanced AV1 technologies are applied to your images through carefully tuned encoding parameters. The superior block partitioning scheme and advanced quantization algorithms are crucial for achieving optimal compression ratios when you convert webp to avif format.
                    </p>
                  </div>
                </div>
              </div>

              {/* Format Comparison Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">WebP vs. AVIF: Technical Comparison</h3>
                    <p className="text-gray-600">
                      While WebP utilizes VP8/VP9 codec technology with limited partition schemes, AVIF implements the more advanced AV1 codec with superior partitioning, advanced prediction modes, and more sophisticated entropy coding.
                      AVIF delivers approximately 50-62% better compression efficiency compared to WebP while maintaining superior perceptual quality, which explains the growing demand to convert webp to avif for optimized web content delivery.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Compatible Input Formats for Conversion</h3>
                    <p className="text-gray-600">
                      Our specialized webp to avif converter is engineered to process WebP files (.webp extension) with full support for both lossy and lossless WebP variants. The conversion pipeline handles all WebP feature sets including alpha transparency, animation sequences, and extended color profiles.
                      The sophisticated converter webp to avif engine preserves all essential image attributes while applying advanced AV1 encoding techniques to achieve optimal compression results for your web content delivery requirements.
                    </p>
                  </div>
                </div>
                <div className="relative">
                  <img
                    src="https://image.heic-tojpg.com/metadata-in-image.webp"
                    alt="WebP vs AVIF Format Comparison"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
              </div>

              {/* Conversion Benefits Group */}
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="relative order-2 md:order-1">
                  <img
                    src="https://cdn.pixabay.com/photo/2015/01/08/18/27/startup-593341_1280.jpg"
                    alt="Benefits of WebP to PNG Conversion"
                    className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                  />
                </div>
                <div className="space-y-8 order-1 md:order-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Strategic Benefits of WebP to AVIF Conversion</h3>
                    <p className="text-gray-600">
                      Converting WebP to AVIF represents a strategic advancement in visual content optimization for modern web applications. With growing browser support for AVIF across Chrome, Firefox, and Safari, implementing a webp to avif conversion strategy ensures your web assets utilize the most efficient image format available.
                      This forward-thinking approach delivers tangible benefits including improved Largest Contentful Paint (LCP) metrics, reduced Content Delivery Network (CDN) costs, and enhanced user experience through faster page loading times.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Technical Advantages of AVIF Format</h3>
                    <p className="text-gray-600">
                      AVIF offers several significant technical advantages including superior compression efficiency, improved perceptual quality, advanced HDR support, and efficient alpha channel encoding. The format's support for wide color gamut and high bit-depth color representation ensures optimal visual fidelity.
                      When you use our webp to avif bulk converter, these technical advantages translate directly into practical benefits for your web content delivery strategy, including reduced storage requirements, lower bandwidth consumption, and improved SEO ranking through enhanced Core Web Vitals performance.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <section className="how-to">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">How to Use Our WebP to AVIF Converter</h2>

            <div className="grid md:grid-cols-2 gap-8 items-center mb-12">
              <div>
                <ol className="space-y-6 relative">
                  <div className="absolute top-0 bottom-0 left-4 w-0.5 bg-indigo-100"></div>
                  <li className="relative pl-12">
                    <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">1</div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">Upload WebP Files</h3>
                    <p className="text-gray-600">
                      Drag and drop your WebP images into the designated conversion area, or click to browse and select files from your device storage. Our webp to avif converter supports bulk uploading of multiple files for simultaneous processing with advanced queue management and parallel encoding capabilities.
                    </p>
                  </li>
                  <li className="relative pl-12">
                    <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">2</div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">Configure Encoding Parameters</h3>
                    <p className="text-gray-600">
                      Fine-tune your webp to avif converter settings to optimize output for your specific use case. Adjust quality levels from 1-100 to balance file size and visual fidelity, and optionally remove metadata for enhanced privacy protection when converting from WebP to AVIF format.
                    </p>
                  </li>
                  <li className="relative pl-12">
                    <div className="absolute left-0 bg-indigo-500 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold">3</div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">Process and Download</h3>
                    <p className="text-gray-600">
                      Click the "Convert" button to initiate the webp to avif bulk conversion process. Our advanced AV1 encoder will process your images with optimal parameters. Once completed, you can download individual AVIF files or use our batch download functionality to retrieve all converted images simultaneously.
                    </p>
                  </li>
                </ol>
              </div>
              <div className="relative">
                <img
                  src="https://cdn.pixabay.com/photo/2015/05/31/10/55/man-791049_1280.jpg"
                  alt="WebP to AVIF Conversion Process"
                  className="rounded-lg shadow-lg w-full h-[400px] object-cover"
                />
              </div>
            </div>
          </section>
        </div>


        <section className="why-use mb-8 mt-12">
          <h2 className="text-2xl font-bold mb-6 text-gray-800">Why Choose Our WebP to AVIF Converter</h2>

          <div className="space-y-16">
            {/* Reason 1 */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Revolutionary Image Optimization Technology</h3>
                <p className="text-gray-600">
                  Our webp to avif converter represents a paradigm shift in image optimization technology. While WebP utilizes VP8/VP9 codec architecture, our AVIF implementation leverages the revolutionary AV1 codec with advanced partition schemes and sophisticated prediction models. This technological evolution ensures your images achieve up to 62% smaller file sizes while maintaining superior perceptual quality.
                </p>
                <p className="text-gray-600">
                  The proprietary encoder pipeline in our convert webp to avif tool implements advanced psychovisual optimization techniques including adaptive quantization, frequency-domain filtering, and context-adaptive entropy coding to deliver unparalleled compression efficiency with minimal artifacts.
                </p>
              </div>
              <div className="relative">
                <img
                  src="https://cdn.pixabay.com/photo/2016/11/23/14/37/blur-1853262_1280.jpg"
                  alt="WebP to AVIF Next-Generation Optimization"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
            </div>

            {/* Reason 2 */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="relative order-2 md:order-1">
                <img
                  src="https://cdn.pixabay.com/photo/2015/07/17/22/43/student-849825_1280.jpg"
                  alt="Simplified WebP to AVIF Workflow"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
              <div className="space-y-4 order-1 md:order-2">
                <h3 className="text-lg font-semibold text-gray-800">Enterprise-Grade Web Performance Optimization</h3>
                <p className="text-gray-600">
                  For modern web architectures, implementing a strategic webp to avif conversion pipeline delivers measurable performance enhancements across key Core Web Vitals metrics. The superior compression efficiency of AVIF directly impacts Largest Contentful Paint (LCP) scores,
                  while maintaining visual fidelity essential for professional web applications and e-commerce platforms.
                </p>
                <p className="text-gray-600">
                  Our webp to avif bulk converter's parallelized processing architecture enables industrial-scale image optimization with intelligent resource allocation. The advanced queue management system supports concurrent AV1 encoding operations, maximizing throughput while maintaining optimal quality for each converted image.
                </p>
              </div>
            </div>

            {/* Reason 3 */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Data Security and Privacy Compliance</h3>
                <p className="text-gray-600">
                  Our webp to avif free converter implements comprehensive data protection measures including TLS 1.3 encryption, ephemeral key management, and secure memory handling. The optional metadata stripping feature ensures GDPR and CCPA compliance by removing potentially sensitive EXIF data
                  from your images during the webp to avif conversion process.
                </p>
                <p className="text-gray-600">
                  The secure processing infrastructure employs containerized computation with strict resource isolation, ensuring your images remain protected throughout the conversion pipeline. All uploaded files are processed in ephemeral memory contexts and automatically purged after processing,
                  providing enterprise-grade security for your visual assets during the webp to avif conversion workflow.
                </p>
              </div>
              <div className="relative">
                <img
                  src="https://cdn.pixabay.com/photo/2017/10/10/21/46/laptop-2838917_1280.jpg"
                  alt="WebP to AVIF Privacy Protection"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
            </div>

            {/* Reason 4 */}
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="relative order-2 md:order-1">
                <img
                  src="https://image.heic-tojpg.com/jfif-to-jpg-web-optimization.webp"
                  alt="WebP to AVIF Quality Preservation"
                  className="rounded-lg shadow-lg w-full h-[300px] object-cover"
                />
              </div>
              <div className="space-y-4 order-1 md:order-2">
                <h3 className="text-lg font-semibold text-gray-800">Next-Generation Visual Quality with AV1 Technology</h3>
                <p className="text-gray-600">
                  Our webp to avif converter implements the full AV1 codec specification with advanced features including variable block-size transforms, non-separable transforms, and sophisticated loop filtering. These technologies ensure preservation of fine image details and textures
                  while achieving revolutionary compression ratios that significantly outperform WebP format efficiency.
                </p>
                <p className="text-gray-600">
                  The AVIF format's support for high bit-depth color representation, wide color gamut, and HDR imaging capabilities ensures your visual content maintains professional-grade quality when you convert webp to avif. This makes our converter the ideal solution for media-rich applications, creative professional workflows, and content delivery networks requiring optimal visual quality with maximum compression efficiency.
                </p>
              </div>
            </div>
          </div>
        </section>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-bold mb-4">Frequently Asked Questions About WebP to AVIF Conversion</h2>
          <div className="space-y-6">
            <div>
              <h3 className="font-semibold text-gray-900">What are the technical differences between WebP and AVIF formats?</h3>
              <p className="mt-1 text-gray-700">
                WebP utilizes Google's VP8/VP9 codec technology with limited partition schemes, while AVIF implements the Alliance for Open Media's AV1 codec with superior partitioning, advanced prediction modes, and more sophisticated entropy coding.
                AVIF delivers approximately 50-62% better compression efficiency compared to WebP while maintaining superior perceptual quality, which explains the growing demand to convert webp to avif for optimized web content delivery.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Does AVIF conversion preserve image quality?</h3>
              <p className="mt-1 text-gray-700">
                When using our specialized webp to avif converter, image quality is not only preserved but often enhanced due to AVIF's advanced encoding techniques. The AV1 codec's superior intra-prediction modes and sophisticated loop filtering produce better perceptual quality at equivalent file sizes compared to WebP.
                Our converter webp to avif pipeline maintains optimal image fidelity through carefully tuned AV1 encoding parameters and advanced psychovisual optimization algorithms.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Is the WebP to AVIF conversion process secure?</h3>
              <p className="mt-1 text-gray-700">
                Yes, our webp to avif free converter implements comprehensive security measures throughout the conversion pipeline. All file transfers utilize TLS 1.3 encryption, while server-side processing occurs in isolated memory contexts with strict access controls.
                We implement a zero-retention policy where all uploaded WebP files and generated AVIF images are automatically purged from our systems after processing, ensuring complete data privacy and security compliance when you convert webp to avif through our platform.
              </p>
            </div>
          </div>
        </div>
      </main>

      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 p-3 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-full shadow-lg hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 z-50"
          aria-label="Scroll to top"
        >
          <FiArrowUp className="w-6 h-6" />
        </button>
      )}
    </>
  );
} 