import { Metadata } from 'next';
import { baseUrl } from '../layout';

// Metadata for WebP to JPG page, optimized for SEO
export const metadata: Metadata = {
  title: 'The Best Free WebP to JPG Converter',
  description: 'Convert WebP to JPG online for free. Fast batch conversion with no watermarks. Preserve optimal image quality. No installation needed!',
  keywords: 'webp to jpg, webp converter, convert webp, webp to jpg converter, webp to jpeg',
  alternates: {
    canonical: `${baseUrl}/webp-to-jpg`,
  },
  openGraph: {
    title: 'The Best Free WebP to JPG Converter',
    description: 'Convert WebP to JPG online for free. Fast batch conversion with no watermarks. Preserve optimal image quality. No installation needed!',
    url: `${baseUrl}/webp-to-jpg`,
    siteName: 'HEIC to JPG Converter',
    images: [
      {
        url: `https://image.heic-tojpg.com/webp-to-jpg-converter.webp`,
        width: 1200,
        height: 630,
        alt: 'The Best Free WebP to JPG Converter',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Best Free WebP to JPG Converter',
    description: 'Convert WebP to JPG online for free. Fast batch conversion with no watermarks. Preserve optimal image quality. No installation needed!',
    images: [`https://image.heic-tojpg.com/professional-webp-to-jpg-converter-tool.webp`],
  },
};

export default function WebpToJpgLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
} 