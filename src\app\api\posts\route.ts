import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';

export async function GET() {
  try {
    const postsDirectory = path.join(process.cwd(), 'data/md');
    const fileNames = fs.readdirSync(postsDirectory);

    const posts = fileNames
      .filter(fileName => fileName.endsWith('.md'))
      .map(fileName => {
        // 读取文件内容
        const fullPath = path.join(postsDirectory, fileName);
        const fileContents = fs.readFileSync(fullPath, 'utf8');

        // 使用 gray-matter 解析 markdown 文件的前置内容
        const { data } = matter(fileContents);

        return {
          title: data.title || '',
          description: data.description || '',
          slug: data.slug || fileName.replace(/\.md$/, ''),
          thumbnail: data.thumbnail || '',
          date: data.date || '',
        };
      })
      // 按日期排序，最新的在前面
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    return NextResponse.json(posts);
  } catch (error) {
    console.error('Error loading posts:', error);
    return NextResponse.json(
      { error: 'Failed to load posts' },
      { status: 500 }
    );
  }
} 