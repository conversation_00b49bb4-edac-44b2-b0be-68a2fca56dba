import React, { useState } from 'react';

interface ResultDisplayProps {
  style: string;
  resultText: string;
}

const ResultDisplay: React.FC<ResultDisplayProps> = ({ style, resultText }) => {
  const [copySuccess, setCopySuccess] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(resultText).then(() => {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    });
  };

  return (
    <div className="mt-4 p-4 bg-gray-100 rounded-lg relative">
      <h4 className="font-semibold mb-2">{style}</h4>
      <pre className="whitespace-pre-wrap text-sm">{resultText}</pre>
      <button
        onClick={copyToClipboard}
        className="absolute bottom-2 right-2 px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
      >
        {copySuccess ? 'Copied!' : 'Copy to Clipboard'}
      </button>
    </div>
  );
};

export default ResultDisplay;